<div class="pl-0">
	<div class="card card-browser-states mb-0">
		<div class="card-header">
			<div class="">
				<h4 class="card-title">Trạng thái</h4>
				<p class="card-text font-small-2">Th<PERSON><PERSON> kê các công việc theo trạng thái</p>
			</div>
			<div class="card-toolbar">
				<div class="btn-group">
					<div ngbDropdown>
						<button
							type="button"
							class="btn btn-outline-primary btn-sm dropdown-toggle"
							data-toggle="dropdown"
							aria-haspopup="true"
							aria-expanded="false"
							ngbDropdownToggle
							rippleEffect
						>
							{{ dashboardFilter === 'content' ? 'Bài viết' : (dashboardFilter === 'qa' ? 'Câu hỏi' : 'Tất cả') }}
						</button>
						<div ngbDropdownMenu>
							<a ngbDropdownItem href="javascript:void(0);" (click)="dashboardFilter = ''; 
								prepareChartData()">Tất cả</a>
							<a ngbDropdownItem href="javascript:void(0);" (click)="dashboardFilter = 'content'; 
								prepareChartData()">Bài viết</a>
							<a ngbDropdownItem href="javascript:void(0);" (click)="dashboardFilter = 'qa'; 
								prepareChartData()">Câu hỏi</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="card-body">
			<ng-container *ngFor="let state of statusStates">
				<div class="d-flex justify-content-between">
					<h6 class="align-self-center mb-0">{{ state.label }}</h6>
					<div class="d-flex align-items-center">
						<div class="font-weight-bold text-body-heading mr-25">{{ state.percentage }}%</div>
						<div [ngClass]="state.chartClass">
							<apx-chart
								[chart]="state.chartOptions.chart"
								[grid]="state.chartOptions.grid"
								[colors]="state.chartOptions.colors"
								[series]="state.chartOptions.series"
								[plotOptions]="state.chartOptions.plotOptions"
								[stroke]="state.chartOptions.stroke"
							></apx-chart>
						</div>
					</div>
				</div>
			</ng-container>
		</div>
	</div>
</div>