<div class="row">
  <div class="col-auto pe-0 border-right">
    <ul
      ngbNav
      #navVertical="ngbNav"
      [(activeId)]="activeTabId"
      (navChange)="onTabChange($event.nextId)"
      class="nav nav-tabs nav-left flex-column"
    >
			<li ngbNavItem="ALL" class="pb-1">
				<a ngbNavLink>Tất cả ({{ allContentCount }})</a>
				<ng-template ngbNavContent></ng-template>
			</li>
      <li ngbNavItem="DRAFT" class="pb-1">
        <a ngbNavLink>Mới khởi tạo ({{ draftContent.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
      <li ngbNavItem="WRITING" class="pb-1">
        <a ngbNavLink>Đang viết ({{ writingContent.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
      <li ngbNavItem="REVIEWING" class="pb-1">
        <a ngbNavLink>Chờ phê duyệt ({{ reviewingContent.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
      <li ngbNavItem="COMPLETED" class="pb-1">
        <a ngbNavLink>Hoàn thành ({{ completedContent.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
      <li ngbNavItem="PUBLISHED" class="pb-1">
        <a ngbNavLink>Đã công bố ({{ publishedContent.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
      <!-- <li ngbNavItem="UNPUBLISHED" class="pb-1">
        <a ngbNavLink>Chưa công bố ({{ unpublishedContent.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li> -->
			<li ngbNavItem="REJECTED" class="pb-1">
        <a ngbNavLink>Từ chối ({{ rejectedContent.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
      <li ngbNavItem="ARCHIVED" class="pb-1">
        <a ngbNavLink>Lưu trữ ({{ archivedContent.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
    </ul>
  </div>

  <div class="col ps-0" style="min-width:0;">
		<!-- [externalPaging]="true" -->
		<!-- [offset]="page - 1" -->
    <ngx-datatable
      #ContentTable
      [rows]="filteredContent"
      [rowHeight]="'auto'"
      class="bootstrap core-bootstrap cursor"
      [columnMode]="ColumnMode.force"
      [footerHeight]="50"
      [scrollbarH]="true"
      [limit]="12"
      [count]="totalItem"
      (activate)="onActivate($event)"
      (page)="onPage($event)"
      (sort)="onSort($event)"
      [sorts]="[{ prop: 'id', dir: 'asc' }]"
    >
      <ngx-datatable-column name="Mã" prop="id" [width]="60" [minWidth]="60" [sortable]="true" [comparator]="compareNumber"></ngx-datatable-column>
      <ngx-datatable-column name="Tiêu đề" prop="title">
        <ng-template ngx-datatable-cell-template let-row="row">
          <div class="text-wrap text-break">
            {{ row.title }}
          </div>
        </ng-template>
      </ngx-datatable-column>
			<ngx-datatable-column name="Người viết" prop="writer.fullname" [width]="120" [minWidth]="60">
				<ng-template ngx-datatable-cell-template let-row="row">
          <div class="text-wrap text-break">
            {{ row.writer?.fullname }}
          </div>
        </ng-template>
			</ngx-datatable-column>
      <ngx-datatable-column name="Người p.duyệt" prop="reviewer.fullname">
        <ng-template ngx-datatable-cell-template let-row="row">
          <div class="text-wrap text-break">
            {{ row.reviewer?.fullname }}
          </div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column name="Ngày công bố" prop="publish_date" [width]="140" [minWidth]="120" [sortable]="true">
        <ng-template ngx-datatable-cell-template let-row="row">
          <span
            [ngClass]="{
              'text-muted': !row.publish_date,
              'text-danger': row.publish_date && isFuture(row.publish_date),
              'text-success': row.publish_date && !isFuture(row.publish_date)
            }"
            [ngbTooltip]="row.publish_date ? (formatYMDHM(row.publish_date) + (isFuture(row.publish_date) ? ' (hẹn lịch)' : '')) : 'Chưa đặt ngày'"
            container="body"
          >
            {{ row.publish_date ? (formatYMD(row.publish_date)) : '—' }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column name="Cập nhật lúc" prop="" [width]="150" [minWidth]="130">
        <ng-template ngx-datatable-cell-template let-row="row">
          <span>{{ formatRelativeTime(row.updated_at || row.created_at) }}</span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column name="Loại bài viết" prop="type" [width]="120" [minWidth]="110" [sortable]="false">
        <ng-template ngx-datatable-cell-template let-row="row">
          <div
            class="badge"
            [ngClass]="{
              'badge-light-dark': row.type === 'NORMAL',
              'badge-light-danger': row.type === 'HOT'
            }"
          >
            {{ row.type === 'NORMAL' ? 'Tin tức' : (row.type === 'HOT' ? 'Nổi bật' : row.type) }}
          </div>
        </ng-template>
      </ngx-datatable-column>
			<ngx-datatable-column *ngIf="activeTabId=='ALL'" name="Trạng thái" prop="status" [width]="120" [minWidth]="100" >
        <ng-template ngx-datatable-cell-template let-row="row">
          <div
            class="badge"
            [ngClass]="{
              'badge-light-dark': row.status === 'DRAFT',
              'badge-light-primary': row.status === 'WRITING' || row.status === 'REVIEWING',
              'badge-light-success': row.status === 'COMPLETED' || row.status === 'PUBLISHED',
              'badge-light-secondary': row.status === 'UNPUBLISHED',
              'badge-light-danger': row.status === 'REJECTED',
              'badge-light-warning': row.status === 'ARCHIVED'
            }"
          >
            {{
              row.status === 'DRAFT' ? 'Mới khởi tạo' :
              row.status === 'WRITING' ? 'Đang viết' :
              row.status === 'REVIEWING' ? 'Chờ phê duyệt' :
              row.status === 'COMPLETED' ? 'Hoàn thành' :
              row.status === 'PUBLISHED' ? 'Đã công bố' :
              row.status === 'UNPUBLISHED' ? 'Chưa công bố' :
              row.status === 'REJECTED' ? 'Từ chối' :
              row.status === 'ARCHIVED' ? 'Lưu trữ' :
              row.status
            }}
          </div>
        </ng-template>
      </ngx-datatable-column>
			<!-- <ngx-datatable-column name="Hiệu lực" prop="is_active" [width]="100" [minWidth]="100" [sortable]="false">
        <ng-template ngx-datatable-cell-template let-row="row">
          <div
            class="badge"
            [ngClass]="{
              'badge-danger': !row.is_active,
              'badge-success': row.is_active
            }"
          >
            {{ row.is_active ? 'Hoạt động' : 'Đã ẩn' }}
          </div>
        </ng-template>
      </ngx-datatable-column> -->
      <ngx-datatable-column
        name=""
        [sortable]="false"
        [width]="90"
        [minWidth]="90"
        [maxWidth]="100"
        [frozenRight]="true"
        [resizeable]="false"
        [canAutoResize]="false"
      >
        <ng-template ngx-datatable-cell-template let-row="row">
          <span container="body">
            <!-- toggle active -->
            <!-- <a
              *ngIf="row.is_active"
              href="javascript:void(0);"
              class="hide-arrow color-333"
              ngbTooltip="Vô hiệu hóa"
              container="body"
              (click)="updateActiveContent(row.id)"
            >
              <i data-feather="slash" class="mx-50"></i>
            </a>

            <a
              *ngIf="!row.is_active"
              href="javascript:void(0);"
              class="hide-arrow color-333"
              ngbTooltip="Kích hoạt lại"
              container="body"
              (click)="updateActiveContent(row.id)"
            >
              <i data-feather="check-circle" class="mx-50"></i>
            </a> -->

            <!-- delete -->
            <a
              href="javascript:void(0);"
              class="hide-arrow color-333"
              ngbTooltip="Xóa"
              container="body"
              (click)="deleteContent(row.id)"
            >
              <i data-feather="trash-2" class="mx-50"></i>
            </a>
          </span>
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>
  </div>
</div>