<div class="modal-header border-0">
  <h4 class="modal-title">Thêm ng<PERSON><PERSON> nhận</h4>
  <div class="text-primary fw-semibold ms-auto">
    <PERSON><PERSON> chọn {{ selectedCount }}
  </div>
</div>

<div class="modal-body">
  <div class="mb-1 position-relative">
    <div class="input-with-icon position-relative">
      <button type="button" class="btn-icon-inside left-icon">
        <i data-feather="search"></i>
      </button>

      <input
        type="text"
        class="form-control"
        placeholder="Tì<PERSON> kiếm theo tổ chức"
        (input)="searchText = $any($event.target).value || ''"
      />
    </div>
  </div>

  <div class="tree-wrap">
    <ng-container *ngFor="let n of data">
      <ng-container
        *ngTemplateOutlet="treeNodeTpl; context: { $implicit: n, level: 0 }"
      ></ng-container>
    </ng-container>
  </div>

  <ng-template #treeNodeTpl let-node let-level="level">
    <div
      *ngIf="matches(node, searchText)"
      class="tree-row"
      [ngStyle]="{'padding-left.px': 16 + level * 20}"
      [class.has-children]="node.children?.length"
      [class.is-partial]="node.partial"
    >
      <span class="tree-expand-holder d-inline-flex align-items-center justify-content-center width-42px height-42px">
        <button
          type="button"
          class="toggle"
          *ngIf="node.children?.length"
          (click)="toggleExpand(node)"
          [attr.aria-label]="node.expanded ? 'Collapse' : 'Expand'"
        >
          <i
            class="feather"
            [ngClass]="{
              'icon-chevron-down': node.expanded,
              'icon-chevron-right': !node.expanded
            }"
          ></i>
        </button>
        <ng-container *ngIf="!node.children?.length">
          <span class="width-48px height-48px"></span>
        </ng-container>
      </span>

      <i
        class="feather node-icon"
        [ngClass]="{
          'icon-briefcase': node.type === 'org',
          'icon-folder': node.type === 'group'
        }"
      ></i>

      <div class="node-text">
        <span class="node-name fw-semibold">
          <strong>{{ node.name }}</strong>
          <ng-container *ngIf="node.email">
            <br />
            <i>{{ node.email }}</i>
          </ng-container>
        </span>
        <span class="node-count" *ngIf="node.children.length">
          ({{ node.children.length }})
        </span>
      </div>

      <div class="checkbox-right">
        <input
          type="checkbox"
          class="form-check-input"
          [checked]="node.checked"
          [indeterminate]="node.partial"
          (change)="onCheck(node, $event.target.checked)"
        />
      </div>
    </div>

    <div *ngIf="node.children?.length && node.expanded">
      <ng-container *ngFor="let c of node.children">
        <ng-container
          *ngTemplateOutlet="
            treeNodeTpl;
            context: { $implicit: c, level: level + 1 }
          "
        ></ng-container>
      </ng-container>
    </div>
  </ng-template>
</div>

<div class="modal-footer border-0">
  <button type="button" class="btn btn-secondary" (click)="cancel()">
    Hủy
  </button>
  <button type="button" class="btn btn-primary-theme" (click)="confirm()">
    Xác nhận
  </button>
</div>