# Multiple User Control Component

## M<PERSON> tả
Component này cho phép tải lên file Excel và tạo danh sách người dùng từ dữ liệu trong file.

## Tính năng
- **Drag & Drop**: Kéo thả file Excel vào vùng upload
- **Click to Upload**: Click vào vùng upload để chọn file
- **File Validation**: Chỉ chấp nhận file .xlsx và .xls
- **Data Parsing**: Tự động đọc và parse dữ liệu từ Excel
- **Data Validation**: Kiểm tra tính hợp lệ của dữ liệu
- **Preview Table**: Hiển thị bảng dữ liệu trước khi lưu

## Cấu trúc file Excel
File Excel cần có các cột sau (tên cột có thể khác nhau):

| Cột bắt buộc  | Tên cột có thể chấp nhận           |
|---------------|------------------------------------|
| Họ tên        | Họ tên, Họ và tên, Full Name, Name |
| Email         | Email, E-mail, Địa chỉ email       |
| Tên đăng nhập | Tên đăng nhập, Username, User name |

| Cột tùy chọn  | Tên cột có thể chấp nhận                       |
|---------------|------------------------------------------------|
| Số điện thoại | Số điện thoại, Phone, Điện thoại, Phone Number |
| Địa chỉ       | Địa chỉ, Address, Địa chỉ thường trú           |

## Ví dụ file Excel
Tạo file Excel với cấu trúc như sau:

| Họ tên        | Email                | Tên đăng nhập | Số điện thoại | Địa chỉ        |
|---------------|----------------------|---------------|---------------|----------------|
| Nguyễn Văn A  | <EMAIL> | nva           | 0123456789    | Hà Nội         |
| Trần Thị B    | <EMAIL>   | ttb           | 0987654321    | TP.HCM         |

**Lưu ý**: 
- Dòng đầu tiên phải là header (tên các cột)
- Có thể sử dụng file template mẫu và mở bằng Excel để tạo file .xlsx
- Tên cột có thể khác nhau miễn là chứa từ khóa tương ứng

## Validation Rules
- **Email**: Phải có định dạng email hợp lệ
- **Tên đăng nhập**: Bắt buộc, ít nhất 3 ký tự
- **Họ tên**: Bắt buộc
- **Số điện thoại**: Nếu có thì phải chứa chỉ số và ký tự hợp lệ

## Sử dụng
1. Truy cập route `/super-admin/user/multiple-user-control`
2. Tải lên file Excel bằng cách kéo thả hoặc click chọn file
3. Click "Xử lý file Excel" để đọc dữ liệu
4. Kiểm tra bảng dữ liệu hiển thị
5. Click "Lưu danh sách người dùng" để lưu