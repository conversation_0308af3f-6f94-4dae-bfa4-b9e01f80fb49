import { Injectable } from "@angular/core";

@Injectable({
  providedIn: "root",
})
export class PaginationService {
  constructor() {}

  paginate<T>(data: T[], currentPage: number, itemsPerPage: number): T[] {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return data.slice(startIndex, startIndex + itemsPerPage);
  }

  getTotalPages(listData: [], itemsPerPage: number): number {
    return Math.ceil(listData.length / itemsPerPage);
  }
}
