import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

import { BehaviorSubject, Observable, Subject } from 'rxjs';

import { AuthenticationService } from 'app/auth/service';
import { User } from 'app/auth/models';

@Injectable({
  providedIn: 'root'
})
export class CoreMenuService {
  currentUser: User;
  onItemCollapsed: Subject<any>;
  onItemCollapseToggled: Subject<any>;

  // Private
  private _onMenuRegistered: BehaviorSubject<any>;
  private _onMenuUnregistered: BehaviorSubject<any>;
  private _onMenuChanged: BehaviorSubject<any>;
  private _currentMenuKey: string;
  private _registry: { [key: string]: any } = {};

  // === ADD: lưu menu gốc để re-filter khi user/cms_roles đổi
  private _registryOriginal: { [key: string]: any } = {};

  /**
   * Constructor
   *
   * @param {Router} _router
   * @param {AuthenticationService} _authenticationService
   */
  constructor(private _router: Router, private _authenticationService: AuthenticationService,) {
    this._authenticationService.currentUser.subscribe(x => {
      this.currentUser = x;
      this.refresh();
    });
    // Set defaults
    this.onItemCollapsed = new Subject();
    this.onItemCollapseToggled = new Subject();

    // Set private defaults
    this._currentMenuKey = null;
    this._onMenuRegistered = new BehaviorSubject(null);
    this._onMenuUnregistered = new BehaviorSubject(null);
    this._onMenuChanged = new BehaviorSubject(null);

    // === ADD: khi cms_roles trong localStorage thay đổi -> re-filter
    window.addEventListener('cms_roles_updated', () => this.refresh());
    window.addEventListener('storage', (e: StorageEvent) => {
      if (e.key === 'cms_roles') this.refresh();
    });
  }

  // Accessors
  // -----------------------------------------------------------------------------------------------------

  /**
   * onMenuRegistered
   *
   * @returns {Observable<any>}
   */
  get onMenuRegistered(): Observable<any> {
    return this._onMenuRegistered.asObservable();
  }

  /**
   * onMenuUnregistered
   *
   * @returns {Observable<any>}
   */
  get onMenuUnregistered(): Observable<any> {
    return this._onMenuUnregistered.asObservable();
  }

  /**
   * onMenuChanged
   *
   * @returns {Observable<any>}
   */
  get onMenuChanged(): Observable<any> {
    return this._onMenuChanged.asObservable();
  }

  public isRegistered(key: string): boolean {
    return !!this._registry[key];
  }

  /** Cập nhật menu nếu đã có; nếu chưa có thì đăng ký mới rồi setCurrent */
  public setMenu(key: string, menu: any): void {
    // Nếu chưa có menu -> đăng ký mới và đặt làm current
    if (!this.isRegistered(key)) {
      this.register(key, menu);
      this.setCurrentMenu(key);
      return;
    }

    // ĐÃ có rồi: chỉ cập nhật bản đang dùng (đÃ lọc) cho key đó.
    // KHÔNG đụng tới _registryOriginal, để giữ bản gốc cho việc re-filter.
    this._registry[key] = menu;

    // Thông báo menu thay đổi để VerticalMenu reload
    this._onMenuChanged.next(key);
  }

  // === ADD: lọc item theo role hoặc cmsRole (nếu có role active)
  private _buildFiltered(menu: any[]): any[] {
    const userRole = this.currentUser?.role as string | undefined;

    let cmsActiveRoles: string[] = [];
    try {
      const raw = localStorage.getItem('cms_roles') || '[]';
      const rows = JSON.parse(raw);
      cmsActiveRoles = (rows || []).filter((r: any) => r?.is_active).map((r: any) => r?.role);
    } catch {}

    const canSee = (item: any): boolean => {
      const passApp = Array.isArray(item.role) ? item.role.includes(userRole) : false;
      const passCms = Array.isArray(item.cmsRole)
        ? item.cmsRole.some((r: string) => cmsActiveRoles.includes(String(r)))
        : false;
      const noReq = !item.role && !item.cmsRole;
      return noReq || passApp || passCms;
    };

    const walk = (items: any[]): any[] =>
      (items || [])
        .filter(canSee)
        .map(i => ({ ...i, children: i.children ? walk(i.children) : undefined }));

    return walk(menu);
  }

  public refresh(): void {
    const keys = Object.keys(this._registryOriginal);
    if (!keys.length) return;

    keys.forEach(key => {
      const original = this._registryOriginal[key];
      this._registry[key] = this._buildFiltered(original);
    });

    if (!this._currentMenuKey && keys.length) {
      this._currentMenuKey = keys[0];
    }

    if (this._currentMenuKey) {
      this._onMenuChanged.next(this._currentMenuKey);
      const current = this._registry[this._currentMenuKey];
      if (current) {
        this._onMenuRegistered.next([this._currentMenuKey, current]);
      }
    }
  }


  // Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Register the provided menu with the provided key
   *
   * @param key
   * @param menu
   */
  register(key, menu): void {
    if (this._registry[key]) {
      console.error(`Menu with the key '${key}' already exists. Either unregister it first or use a unique key.`);
      return;
    }

    // lưu menu gốc
    this._registryOriginal[key] = menu;
    const filtered = this._buildFiltered(menu);

    this._registry[key] = filtered;
    this._onMenuRegistered.next([key, filtered]);
  }

  /**
   * Unregister the menu from the registry
   *
   * @param key
   */
  unregister(key): void {
    // Confirm if the menu exists
    if (!this._registry[key]) {
      console.warn(`Menu with the key '${key}' doesn't exist in the registry.`);
    }

    // Unregister sidebar
    delete this._registry[key];
    delete this._registryOriginal[key];

    // Notify subject
    this._onMenuUnregistered.next(key);
  }

  /**
   * Get menu from registry by key
   *
   * @param key
   * @returns {any}
   */
  getMenu(key): any {
    // Confirm if the menu exists
    if (!this._registry[key]) {
      console.warn(`Menu with the key '${key}' doesn't exist in the registry.`);
      return;
    }

    // Return sidebar
    return this._registry[key];
  }

  /**
   * Get current menu
   *
   * @returns {any}
   */
  getCurrentMenu(): any {
    if (!this._currentMenuKey) {
      console.warn(`The current menu is not set.`);
      return;
    }

    return this.getMenu(this._currentMenuKey);
  }

  /**
   * Set menu with the key as the current menu
   *
   * @param key
   */
  setCurrentMenu(key): void {
    // Confirm if the sidebar exists
    if (!this._registry[key]) {
      console.warn(`Menu with the key '${key}' doesn't exist in the registry.`);
      return;
    }

    // Set current menu key
    this._currentMenuKey = key;

    // Notify subject
    this._onMenuChanged.next(key);
  }
}