import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { AuthenticationService } from 'app/auth/service';

interface DashboardStatusConfig {
  label: string;
  color: string;
  chartClass: string;
  statusKey: string;
}

@Component({
  selector: 'app-cms-dashboard',
  templateUrl: './cms-dashboard.component.html',
  styleUrls: ['./cms-dashboard.component.scss']
})
export class CmsDashboardComponent implements OnInit, OnChanges {
  @Input() public cmsData: any;
  @Input() public writerUsers: any;
  @Input() public reviewerUsers: any;
  @Input() public adminUsers: any;

  public dashboardFilter = '';

  // ====== ROLE FLAGS ======
  public isWriter = false;
  public isReviewer = false;
  public isAdmin = false;

  // ====== MÀU SẮC ======
  public colors = {
    solid: {
      primary: '#008fe3',
      secondary: '#82868b',
      success: '#28C76F',
      info: '#00cfe8',
      warning: '#FF9F43',
      danger: '#EA5455',
      dark: '#4b4b4b',
      black: '#000',
      white: '#fff',
      body: '#f8f8f8'
    },
    light: {
      primary: '#a0d2fa',
      secondary: '#82868b1a',
      success: '#28C76F1a',
      info: '#00cfe81a',
      warning: '#FF9F431a',
      danger: '#EA54551a',
      dark: '#4b4b4b1a'
    }
  };

  private $trackBgColor = '#EBEBEB';

  public baseChartOptions = {
    chart: {
      height: 30,
      width: 30,
      type: 'radialBar'
    },
    grid: {
      show: false,
      padding: {
        left: -15,
        right: -15,
        top: -12,
        bottom: -15
      }
    },
    plotOptions: {
      radialBar: {
        hollow: {
          size: '22%'
        },
        track: {
          background: this.$trackBgColor
        },
        dataLabels: {
          showOn: 'always',
          name: {
            show: false
          },
          value: {
            show: false
          }
        }
      }
    },
    stroke: {
      lineCap: 'round'
    }
  };

  public statusStates: {
    label: string;
    chartClass: string;
    color: string;
    percentage: number;
    chartOptions: any;
  }[] = [];

  private chartConfigs: DashboardStatusConfig[] = [
    {
      label: 'Mới khởi tạo',
      color: this.colors.solid.secondary,
      chartClass: 'state-chart-draft',
      statusKey: 'DRAFT'
    },
    {
      label: 'Đang viết',
      color: this.colors.solid.primary,
      chartClass: 'state-chart-writing',
      statusKey: 'WRITING'
    },
    {
      label: 'Chờ phê duyệt',
      color: this.colors.solid.primary,
      chartClass: 'state-chart-review',
      statusKey: 'REVIEWING'
    },
    {
      label: 'Hoàn thành',
      color: this.colors.solid.success,
      chartClass: 'state-chart-completed',
      statusKey: 'COMPLETED'
    },
    {
      label: 'Đã công bố',
      color: this.colors.solid.success,
      chartClass: 'state-chart-published',
      statusKey: 'PUBLISHED'
    },
    // {
    //   label: 'Chưa công bố',
    //   color: this.colors.solid.dark,
    //   chartClass: 'state-chart-published',
    //   statusKey: 'UNPUBLISHED'
    // },
    {
      label: 'Bị từ chối',
      color: this.colors.solid.danger,
      chartClass: 'state-chart-rejected',
      statusKey: 'REJECTED'
    },
    {
      label: 'Đã lưu trữ',
      color: this.colors.solid.warning,
      chartClass: 'state-chart-archived',
      statusKey: 'ARCHIVED'
    }
  ];

  constructor(private _auth: AuthenticationService) {}

  // ================== ROLE HELPERS ==================

  private getMyUserId(): number | null {
    const me = this._auth?.currentUserValue;
    return me?.id ?? null;
  }

  private getEntityId(entity: any): number | null {
    if (entity == null) return null;
    if (typeof entity === 'number') return entity;
    if (typeof entity === 'object') {
      return entity.id ?? entity.user?.id ?? entity.user_info?.id ?? entity.user_id ?? null;
    }
    return null;
  }

  private computeRoles(): void {
    const myId = this.getMyUserId();
    const hasMe = (arr: any[]) =>
      Array.isArray(arr) &&
      arr.some(u => {
        const id = this.getEntityId(u?.user ?? u?.user_info ?? u);
        return id === myId;
      });

    this.isWriter = hasMe(this.writerUsers || []);
    this.isReviewer = hasMe(this.reviewerUsers || []);
    this.isAdmin = hasMe(this.adminUsers || []);
  }

  /**
   * Áp scope writer:
   * - Admin/Reviewer: thấy tất cả
   * - Writer: chỉ thấy bài mà writer == chính mình
   * - Khác: hiện tại cho thấy tất cả (nếu cần có role khác thì chỉnh thêm)
   */
  private applyWriterScope(list: any[]): any[] {
    if (this.isAdmin || this.isReviewer) return list;

    if (this.isWriter) {
      const myId = this.getMyUserId();
      if (!myId) return [];
      return list.filter(item => this.getEntityId(item?.writer) === myId);
    }

    return list;
  }

  // ================== LIFECYCLE ==================

  ngOnInit(): void {
    this.computeRoles();
    this.prepareChartData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // thay đổi danh sách user hoặc dữ liệu cms => tính lại role + chart
    if ('writerUsers' in changes || 'reviewerUsers' in changes || 'adminUsers' in changes) {
      this.computeRoles();
    }
    if ('cmsData' in changes || 'writerUsers' in changes || 'reviewerUsers' in changes || 'adminUsers' in changes) {
      this.prepareChartData();
    }
  }

  // ================== LOGIC CHART ==================

  /**
   * Chuẩn bị dữ liệu statusStates cho template,
   * lấy phần trăm từ trạng thái của statusKey trong chartConfigs
   */
  prepareChartData(): void {
    const statusPercentages = this.aggregateStatusPercentages();

    this.statusStates = this.chartConfigs.map(config => {
      const percentage = statusPercentages[config.statusKey] ?? 0;

      const chartOptions = JSON.parse(JSON.stringify(this.baseChartOptions));
      chartOptions.colors = [config.color];
      chartOptions.series = [percentage];

      return {
        label: config.label,
        chartClass: config.chartClass,
        color: config.color,
        percentage,
        chartOptions
      };
    });
  }

  /**
   * Tổng hợp tỷ lệ trạng thái cho các statusKey có trong chartConfigs
   * Tính theo dashboardFilter: '', 'content', hoặc 'qa'
   * Đồng thời áp dụng scope theo role (giống list-post)
   */
  aggregateStatusPercentages(): { [k: string]: number } {
    let items: any[] = [];

    // 1. Lấy list item theo filter content/qa
    if (this.cmsData) {
      if (this.dashboardFilter === 'content') {
        if (Array.isArray(this.cmsData.content)) items = items.concat(this.cmsData.content);
      } else if (this.dashboardFilter === 'qa') {
        if (Array.isArray(this.cmsData.qa)) items = items.concat(this.cmsData.qa);
      } else {
        if (Array.isArray(this.cmsData.content)) items = items.concat(this.cmsData.content);
        if (Array.isArray(this.cmsData.qa)) items = items.concat(this.cmsData.qa);
      }
    }

    // 2. Áp scope theo role: Writer chỉ thấy bài của mình,
    // Admin/Reviewer thấy tất cả (giống các getter draftContent, writingContent,... trong list-post)
    items = this.applyWriterScope(items);

    const total = items.length || 1;

    // Lấy danh sách các statusKey dùng để hiển thị
    const statusKeys = this.chartConfigs.map(cfg => cfg.statusKey);

    // Đếm số lượng item theo statusKey
    const statusCounts: { [k: string]: number } = {};
    for (const key of statusKeys) {
      statusCounts[key] = 0;
    }

    items.forEach(item => {
      if (item && item.status) {
        const key = typeof item.status === 'string' ? item.status.toUpperCase() : '';
        if (statusCounts.hasOwnProperty(key)) {
          statusCounts[key]++;
        }
      }
    });

    // Tính phần trăm cho từng statusKey hiển thị
    const percentages: { [k: string]: number } = {};
    for (const key of statusKeys) {
      percentages[key] = parseFloat(((statusCounts[key] / total) * 100).toFixed(1));
    }

    return percentages;
  }
}
