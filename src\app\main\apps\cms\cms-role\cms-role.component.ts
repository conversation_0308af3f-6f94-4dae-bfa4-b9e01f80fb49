import { Component, ViewEncapsulation, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { CmsService } from '../cms.service';
import { ColumnMode } from '@swimlane/ngx-datatable';
import { debounceTime } from "rxjs/operators";
import { Subject } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from '../../super-admin/user/user.service';
import { repeaterAnimation } from './cms-role.animation';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-cms-role',
  templateUrl: './cms-role.component.html',
  styleUrls: ['./cms-role.component.scss'],
  encapsulation: ViewEncapsulation.None,
  animations: [repeaterAnimation],
})

export class CmsRoleComponent implements OnInit, OnD<PERSON>roy {
  @Output() reloadCmsUser: EventEmitter<any> = new EventEmitter<any>();

  public modalAddUserRef!: NgbModalRef;

  public cmsUserList: any[] = [];
  public cmsUserTotal: number = 0;

  public search: string = '';
  public searchChanged = new Subject<string>();

  public selectedRole: string = null;

  public editingRole = {};
  public ColumnMode = ColumnMode;

  public page: number = 1;

  public userList: any[] = [];
  public userPage: number = 1;
  public isLoading = false;
  public hasMore = true;
  public addUserList = [
    { user_id: null, role: 'WRITER' }
  ];

  constructor(
    private cmsService: CmsService,
    private _toastSerive: ToastrService,
    private _modalService: NgbModal,
    private _userService: UserService,
  ) { }

  getUserList(search = null) {
    if (this.isLoading || !this.hasMore) return;

    this.isLoading = true;

    const params: any = { page: this.userPage };
    if (search) {
      params.search = search;
    }

    this._userService.get(params).subscribe((res) => {
      const newUsers = res.results.map(u => {
        const existsInCmsUser = this.cmsUserList.some(
          (cmsU: any) => cmsU.user && cmsU.user.id === u.id
        );
        const existsInAddUser = this.addUserList.some(
          (addU: any) => addU.user_id === u.id
        );
        return (existsInCmsUser || existsInAddUser) ? { ...u, disabled: true } : u;
      });

      this.userList = [...this.userList, ...newUsers];
      if (!res.next) {
        this.hasMore = false;
      } else {
        this.userPage++;
      }
      this.isLoading = false;
    });
  }

  private searchUserTimeout: any;
  private lastSearchTerm: string = null;
  onSearchUser(event: any) {
    clearTimeout(this.searchUserTimeout);

    this.searchUserTimeout = setTimeout(() => {
      this.userPage = 1;
      this.hasMore = true;
      this.userList = [];
      this.lastSearchTerm = event.term;
      this.getUserList(event.term);
    }, 400);
  }

  onUserSelectBlur() {
    if (this.lastSearchTerm) {
      this.userPage = 1;
      this.hasMore = true;
      this.userList = [];
      this.lastSearchTerm = null;
      this.getUserList();
    }
  }

  updateUserListDisabled() {
    this.userList = this.userList.map(u => {
      const exists = this.addUserList.some(addU => addU.user_id === u.id);
      return exists ? { ...u, disabled: true } : { ...u, disabled: false };
    });
  }

  getCmsUserList() {
    const params: any = { page: this.page };
    if (this.search) params.q = this.search;
    if (this.selectedRole) params.role = this.selectedRole;
    this.cmsService.listRole(params).subscribe(
      (res: any) => {
        this.cmsUserList = res.results || [];
        this.cmsUserTotal = res.count || 0;
      },
      (error) => {
        this.cmsUserList = [];
        this.cmsUserTotal = 0;
      }
    );
  }

  inlineEditingUpdateRole(event, cell, rowIndex) {
    this.editingRole[rowIndex + '-' + cell] = false;
    this.cmsService.updateRole(
      this.cmsUserList[rowIndex].id, 
      { role: event.target.value }
    ).subscribe(
      (res) => {
        this.cmsUserList[rowIndex][cell] = event.target.value;
        this.cmsUserList = [...this.cmsUserList];
      },
      (err) => {
        if (err.detail = "Can't update role of user who has content or QA.") {
          this._toastSerive.error(
            "Không thể cập nhật vai trò cho người dùng này vì họ đã phụ trách thẻ công việc",
            "Thất bại"
          )
        } else {
          this._toastSerive.error(
            "Có lỗi xảy ra khi cập nhật",
            "Thất bại"
          )
        }
      }
    );
  }

  onActivate(event: any) {}

  onPage(event: any) {
    if (event && event.offset != null) {
      this.page = event.offset + 1;
    }
    this.getCmsUserList();
  }

  searchDebounce() {
    this.searchChanged.pipe(
      debounceTime(400)
    ).subscribe((value) => {
      this.page = 1;
      this.search = value;
      this.getCmsUserList();
    });
  }

  onRoleChange() {
    this.page = 1;
    this.getCmsUserList();
  }

  openModal(modal) {
    this.resetAddUserList();
    this.modalAddUserRef = this._modalService.open(
      modal, 
      { scrollable: true },
    );
  }

  addUserToList() {
    this.addUserList.push(
      { user_id: null, role: 'WRITER' }
    );
  }

  deleteUser(id) {
    for (let i = 0; i < this.addUserList.length; i++) {
      if (this.addUserList.indexOf(this.addUserList[i]) === id) {
        this.addUserList.splice(i, 1);
        break;
      }
    }
    this.updateUserListDisabled();
  }

  resetAddUserList() {
    this.addUserList = [{ user_id: null, role: 'WRITER' }];
  }

  deleteCmsUser(row) {
    Swal.fire({
      title: 'Xác nhận xóa?',
      text: 'Bạn có chắc muốn xóa người dùng này khỏi CMS?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy'
    }).then((result) => {
      if (result.isConfirmed) {
        this.cmsService.deleteRole(row.id).subscribe(
          (res) => {
            this._toastSerive.success(
              "Đã xóa người dùng khỏi CMS",
              "Thành công"
            ),
            this.getCmsUserList();
          },
          (err) => {
            this._toastSerive.error(
              "Bạn không có quyền hoặc người dùng này đang được gán công việc CMS",
              "Không thể xóa"
            )
          }
        )
      }
    });
  }

  get isAddUserDisabled(): boolean {
    return (
      this.addUserList.length === 0 ||
      this.addUserList.some(u => !u.user_id || !u.role)
    );
  }

  create() {
    this.cmsService.addMultiple({ data: this.addUserList }).subscribe({
      next: (res) => {
        this._toastSerive.success(
          "Đã thêm người dùng",
          "Thành công"
        );
        this.getCmsUserList();
        if (this.modalAddUserRef) {
          this.modalAddUserRef.close();
        }
      },
      error: (err) => {
        console.error('Error adding users:', err);
      }
    });
  }

  ngOnInit(): void {
    this.getCmsUserList();
    this.getUserList();
    this.searchDebounce();
  }

  ngOnDestroy(): void {}
}
