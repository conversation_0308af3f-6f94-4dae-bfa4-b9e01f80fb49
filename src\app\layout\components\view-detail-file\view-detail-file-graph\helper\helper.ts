/**
 * Helper functions for graph component
 */
import { ApiRelationship } from '../types/graph.types';

/**
 * Converts node type code to Vietnamese label
 */
export function getVietnameseLabel(nhan: string): string {
  switch (nhan) {
    case "VAN_BAN":
      return "Văn bản";
    case "DIEU_KHOAN":
      return "Điều khoản";
    default:
      return "Văn bản";
  }
}

/**
 * Converts relationship type code to Vietnamese label
 */
export function getVietnameseRelationshipLabel(relationshipType: string): string {
  switch (relationshipType) {
    case "bai_bo":
      return "Bãi bỏ";
    case "bao_gom":
      return "<PERSON>o gồm";
    case "bo_sung":
      return "B<PERSON> sung";
    case "can_cu":
      return "Căn cứ";
    case "dan_chieu":
      return "Dẫn chiếu";
    case "dinh_chi":
      return "Đình chỉ";
    case "huong_dan":
      return "Hướng dẫn";
    case "quy_dinh_chi_tiet":
      return "Quy định chi tiết";
    case "sua_doi":
      return "Sửa đổi";
    case "sua_doi_bo_sung":
      return "S<PERSON>a đổi, bổ sung";
    case "thay_the":
      return "Thay thế";
    default:
      return relationshipType; // Return original if no translation found
  }
}

/**
 * Maps relationship strength to grayscale color
 */
export function mapStrengthToColor(strength?: number): string {
  // Map known strengths to grayscale from darkest to lightest
  if (strength === 1.0) return "rgba(0, 0, 0, 0.95)";
  if (strength === 0.9) return "rgba(0, 0, 0, 0.6)";
  if (strength === 0.8) return "rgba(0, 0, 0, 0.35)";
  if (strength === 0.7) return "rgba(0, 0, 0, 0.2)";
  if (strength === 0.5) return "rgba(0, 0, 0, 0.1)";
  // Fallback
  return "#777";
}

/**
 * Extracts node ID from API node object
 */
export function getNodeIdFromApiNode(apiNode: any): string {
  if (!apiNode) return "";
  const idNode = apiNode.thuoc_tinh?.ID
  return String(idNode ?? "");
}

/**
 * Generates a unique key for a relationship
 * Used for deduplication and tracking
 */
export function generateRelationshipKey(rel: ApiRelationship): string {
  return `${String(rel.source_id)}_${String(rel.target_id)}_${String(rel.loai_moi_quan_he || '')}_${String(rel.huong || '')}`;
}

/**
 * Safely converts value to array
 */
export function safeArray<T>(value: T[] | undefined | null): T[] {
  return Array.isArray(value) ? value : [];
}

/**
 * Truncates label string to max length
 */
export function truncate(label: string, maxChars = 10): string {
  return label.length > maxChars ? `${label.slice(0, maxChars)}...` : label;
}

/**
 * Formats ISO date string to DD/MM/YYYY Vietnamese format
 */
export function formatDate(dateString: string): string {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleDateString("vi-VN");
}

/**
 * Filters options based on search term
 * Generic function to filter any array of options with label property
 */
export function filterOptions<T extends { label: string; value: any }>(
  options: T[],
  searchTerm: string
): T[] {
  const normalizedSearch = searchTerm.toLowerCase().trim();
  if (!normalizedSearch) {
    return options;
  }
  return options.filter(option => 
    option.label.toLowerCase().includes(normalizedSearch)
  );
}

/**
 * Formats text for tooltip display by breaking it into lines
 * @param text - The text to format
 * @param wordsPerLine - Number of words per line (default: 5)
 * @returns Formatted text with <br/> tags between lines
 */
export function formatTooltipText(text: string, wordsPerLine: number = 5): string {
  if (!text) {
    return "";
  }
  const words = text.split(/\s+/);
  const lines: string[] = [];
  for (let i = 0; i < words.length; i += wordsPerLine) {
    lines.push(words.slice(i, i + wordsPerLine).join(" "));
  }
  return lines.join("<br/>");
}

