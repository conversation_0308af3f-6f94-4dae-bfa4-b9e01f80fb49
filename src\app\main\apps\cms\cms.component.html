<div class="container p-0">
	<app-breadcrumb [breadcrumb]="breadcrumb"></app-breadcrumb>

	<div class="card">
    <div class="card-body">
			<ul ngbNav #nav="ngbNav" class="nav-tabs">
				<li ngbNavItem id="app-list-work">
					<a ngbNavLink><span data-feather="trello"></span> Bảng theo dõi trạng thái công việc</a>
					<ng-template ngbNavContent>
						<app-list-work 
							[cmsData]="cmsData"
							[writerUsers]="writerUsers"
							[reviewerUsers]="reviewerUsers"
							[adminUsers]="adminUsers"
							(createdOrUpdated)="getCmsData()"
							(reloadCmsUser)="listCmsUser()"
						></app-list-work>
					</ng-template>
				</li>
				<li ngbNavItem id="app-list-post">
					<a ngbNavLink><span data-feather="pen-tool"></span> Danh sách bài viết ({{ visibleContentCount }})</a>
					<ng-template ngbNavContent>
						<app-list-post 
							[content]="cmsData?.content ?? []"
							[writerUsers]="writerUsers"
							[reviewerUsers]="reviewerUsers"
							[adminUsers]="adminUsers"
							(createdOrUpdated)="getCmsData()"
						></app-list-post>
					</ng-template>
				</li>
				<li ngbNavItem id="app-list-question">
					<a ngbNavLink><span data-feather="search"></span> Danh sách câu hỏi ({{ visibleQaCount }})</a>
					<ng-template ngbNavContent>
						<app-list-question 
							[qa]="cmsData.qa"
							[writerUsers]="writerUsers"
							[reviewerUsers]="reviewerUsers"
							[adminUsers]="adminUsers"
							(createdOrUpdated)="getCmsData()"
						></app-list-question>
					</ng-template>
				</li>
				<!-- <li ngbNavItem id="app-list-legal-document">
					<a ngbNavLink>Danh sách văn bản pháp luật</a>
					<ng-template ngbNavContent>
						<app-list-legal-document [cmsData]="cmsData"></app-list-legal-document>
					</ng-template>
				</li> -->
			</ul>

			<div [ngbNavOutlet]="nav" class="mt-2"></div>
		</div>
	</div>
</div>