import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  GraphApiResponse,
  ApiNode,
  DocumentData,
  NodeAttributes,
} from '../types/graph.types';
import { getNodeIdFromApiNode, safeArray, formatDate } from '../helper/helper';
import { ViewDetailFileService } from '../../view-detail-file.service';
import { DetailWorkSpaceService } from 'app/main/apps/quan-ly-van-ban/detail-work-space/detail-work-space.service';
import { ToastrService } from 'ngx-toastr';

export interface DocumentListItem {
  id: string;
  title: string;
  selected: boolean;
  apiNode: ApiNode;
}

/**
 * Service for managing document list operations
 */
@Injectable({
  providedIn: 'root',
})
export class DocumentListService {
  constructor(
    private viewDetailFile: ViewDetailFileService,
    private workspace: DetailWorkSpaceService,
    private toast: ToastrService
  ) {}

  /**
   * Update document list from graph data
   */
  updateDocumentList(
    graphData: GraphApiResponse | null,
    hiddenNodeIds: Set<string>,
    existingDocumentList: DocumentListItem[],
    existingSelectedFiles: any[]
  ): {
    documentList: DocumentListItem[];
    selectedFiles: any[];
  } {
    if (!graphData) {
      return {
        documentList: [],
        selectedFiles: [],
      };
    }

    const apiNodes = safeArray(graphData.nodes);
    const documentNodes = apiNodes.filter((node: ApiNode) => {
      const nodeId = getNodeIdFromApiNode(node);
      // Filter only document nodes (not clauses) and exclude hidden nodes
      return node.nhan_ui === 'Văn bản' && !hiddenNodeIds.has(nodeId);
    });

    // Get IDs of current document nodes
    const currentDocumentIds = new Set(
      documentNodes.map((node: ApiNode) => getNodeIdFromApiNode(node))
    );

    // Remove from selectedFiles any documents that are no longer in the list
    const updatedSelectedFiles = existingSelectedFiles.filter((f) =>
      currentDocumentIds.has(String(f.id))
    );

    // Create a map of existing selections to preserve them
    const existingSelections = new Map<string, boolean>();
    existingDocumentList.forEach((doc) => {
      existingSelections.set(doc.id, doc.selected);
    });

    // Build new document list
    const documentList = documentNodes.map((node: ApiNode) => {
      const nodeId = getNodeIdFromApiNode(node);
      const thuocTinh = node.thuoc_tinh || {};

      // Create label: loai_van_ban + so_hieu
      const loaiVanBan = thuocTinh.loai_van_ban || '';
      const soHieu = thuocTinh.so_hieu || '';
      const title =
        loaiVanBan && soHieu
          ? `${loaiVanBan} - ${soHieu}`
          : node.id_ui || nodeId || 'Văn bản';

      // Preserve existing selection if node already exists, otherwise default to false
      const wasSelected = existingSelections.get(nodeId) || false;

      return {
        id: nodeId,
        title: title,
        selected: wasSelected,
        apiNode: node,
      };
    });

    return {
      documentList,
      selectedFiles: updatedSelectedFiles,
    };
  }

  /**
   * Convert ApiNode to document data format
   */
  convertApiNodeToDocumentData(apiNode: ApiNode): DocumentData {
    const nodeData: NodeAttributes = apiNode.thuoc_tinh || (apiNode as any);

    return {
      ten_day_du: nodeData.ten_day_du || apiNode.ten_day_du || '',
      so_hieu: nodeData.so_hieu || '',
      ngay_ban_hanh: nodeData.ngay_ban_hanh || '',
      loai_van_ban: nodeData.loai_van_ban || '',
      ngay_co_hieu_luc: nodeData.ngay_co_hieu_luc || '',
      ngay_dang_cong_bao: nodeData.ngay_dang_cong_bao || '',
      co_quan_ban_hanh: nodeData.co_quan_ban_hanh || '',
      chuc_danh: nodeData.chuc_danh || '',
      nguoi_ky: nodeData.nguoi_ky || '',
      pham_vi: nodeData.pham_vi || '',
      trich_yeu: nodeData.trich_yeu || '',
      tinh_trang_hieu_luc: nodeData.tinh_trang_hieu_luc || '',
      thoi_gian_cap_nhat: nodeData.thoi_gian_cap_nhat || '',
    };
  }

  /**
   * Save selected documents to workspace
   */
  saveDocuments(
    selectedFiles: any[],
    workspaceId: string
  ): Observable<any> {
    if (!selectedFiles || selectedFiles.length === 0) {
      throw new Error('Không có tài liệu nào được chọn');
    }

    const filesWithWorkspaceId = selectedFiles.map((file) => {
      const { ten_day_du, ...rest } = file || {};
      return {
        ...rest,
        title: ten_day_du || '',
        workspace_id: workspaceId,
      };
    });

    return this.viewDetailFile.addFileBySearch(filesWithWorkspaceId);
  }

  /**
   * Handle save response
   */
  handleSaveResponse(response: any): void {
    const toastFn =
      response.status === 'warning' ? this.toast.warning : this.toast.success;
    toastFn.call(this.toast, response.message, response.status_title, {
      closeButton: true,
      positionClass: 'toast-top-right',
      toastClass: 'toast ngx-toastr',
      enableHtml: true,
    });
    this.workspace.isSaveFileFromSearch.next(true);
  }

  /**
   * Handle save error
   */
  handleSaveError(error: any): string {
    return (
      error?.detail ||
      error?.error?.message ||
      error?.message ||
      'Không thể lưu tài liệu'
    );
  }
}

