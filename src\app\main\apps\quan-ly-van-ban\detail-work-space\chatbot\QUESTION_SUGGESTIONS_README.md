# Tính năng Câu hỏi Gợi ý (Question Suggestions)

## Tổng quan

Tính năng này cho phép hiển thị danh sách câu hỏi gợi ý dưới mỗi câu trả lời của chatbot, giúp người dùng có thể tiếp tục cuộc hội thoại một cách tự nhiên.

## Các API đã thêm

### 1. API Insert Question Suggestion

- **Endpoint**: `POST /question-suggestions/insert`
- **Mục đích**: Lưu câu hỏi của người dùng vào database để làm gợi ý cho các lần sau
- **Body**:

```json
{
  "question": "string",
  "workspace_id": "string"
}
```

### 2. API Search Question Suggestions  

- **Endpoint**: `GET /question-suggestions/search`
- **Mục đích**: L<PERSON>y danh sách câu hỏi gợi ý cho workspace
- **Query params**:

```
workspace_id: string
```

- **Response**:

```json
[
  {
    "question": "string",
    "id": "string"
  }
]
```

## Luồng hoạt động

1. **Khi người dùng gửi câu hỏi**:
   - Nếu không phải từ suggestion → Gọi API `insert` để lưu câu hỏi
   - Nếu từ suggestion → Không gọi API `insert`

2. **Khi stream câu trả lời hoàn thành**:
   - Gọi API `search` để lấy danh sách câu hỏi gợi ý mới nhất
   - Hiển thị danh sách dưới câu trả lời cuối cùng

3. **Khi người dùng click vào câu hỏi gợi ý**:
   - Đặt flag `isFromSuggestion = true`
   - Điền câu hỏi vào input và gửi
   - Hiển thị như người dùng đã hỏi câu hỏi đó

## Giao diện

### Vị trí hiển thị

- Chỉ hiển thị dưới message assistant cuối cùng
- Chỉ hiển thị khi `checkDoneAnswer = true` (stream đã hoàn thành)
- Chỉ hiển thị khi có câu hỏi gợi ý

### Thiết kế

- Tiêu đề: "Các câu hỏi khác cùng chủ đề đã được vấn bởi Chuyên gia pháp luật"
- Danh sách câu hỏi dạng button có thể click
- Hover effect với màu xanh
- Responsive design

## Các file đã thay đổi

1. **chatbot.service.ts**: Thêm 2 API methods
2. **chatbot.component.ts**:
   - Thêm properties: `questionSuggestions`, `isFromSuggestion`
   - Thêm methods: `loadQuestionSuggestions()`, `onSuggestionClick()`, `isLastAssistantMessage()`
   - Cập nhật logic gọi API trong `sendMessage()` và `clickSendMessage()`
3. **chatbot.component.html**: Thêm template hiển thị câu hỏi gợi ý
4. **chatbot.component.scss**: Thêm styles cho question suggestions

## Cách test

### Test với Mock Data (khi backend chưa sẵn sàng)

1. Mở file `chatbot.service.ts`
2. Đổi `USE_MOCK_QUESTION_SUGGESTIONS = false` thành `true`
3. Gửi một vài câu hỏi trong chatbot
4. Sau khi nhận được câu trả lời, sẽ hiển thị phần "Các câu hỏi khác..." với mock data
5. Click vào một câu hỏi gợi ý và kiểm tra xem nó có được gửi như câu hỏi mới không

### Test với Real API (khi backend đã sẵn sàng)

1. Đảm bảo `USE_MOCK_QUESTION_SUGGESTIONS = false` trong `chatbot.service.ts`
2. Gửi một vài câu hỏi trong chatbot
3. Kiểm tra network tab để đảm bảo API được gọi đúng:
   - `POST /question-suggestions/insert` khi gửi câu hỏi
   - `GET /question-suggestions/search` sau khi stream hoàn thành
4. Sau khi nhận được câu trả lời, kiểm tra xem có hiển thị phần "Các câu hỏi khác..." không
5. Click vào một câu hỏi gợi ý và kiểm tra xem nó có được gửi như câu hỏi mới không

## Lưu ý

- API backend cần được implement để tính năng hoạt động đầy đủ
- Có thể sử dụng mock data để test giao diện trước khi backend sẵn sàng
- Cần đảm bảo workspace_id được truyền đúng cho các API calls
- Câu hỏi gợi ý chỉ hiển thị cho message assistant cuối cùng
- Khi click vào suggestion, không gọi API insert để tránh duplicate
