import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { isVPQHDomain, VPQH_COLOR_CODE } from 'app/shared/image.helper';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private _themeSubject = new BehaviorSubject<string>('#008fe3'); // Default purple
  public theme$ = this._themeSubject.asObservable();

  constructor() {
    // Check hostname first - if VPQH domain, set VPQH color before login
    if (isVPQHDomain()) {
      this.setTheme(VPQH_COLOR_CODE);
    } else {
      // Otherwise, load theme from localStorage on init
      const savedTheme = localStorage.getItem('theme-color');
      if (savedTheme) {
        this.setTheme(savedTheme);
      }
    }
  }

  setTheme(color: string): void {
    // Convert hex to RGB for better color manipulation
    const rgb = this.hexToRgb(color);
    
    // Update CSS custom properties
    document.documentElement.style.setProperty('--primary-color', color);
    if (rgb) {
      document.documentElement.style.setProperty('--primary-color-rgb', `${rgb.r},${rgb.g},${rgb.b}`);
      // Create hover and active variants
      const hoverColor = this.lightenColor(color, 10);
      const activeColor = this.darkenColor(color, 10);
      document.documentElement.style.setProperty('--primary-color-hover', hoverColor);
      document.documentElement.style.setProperty('--primary-color-active', activeColor);
    }
    
    // Save to localStorage
    localStorage.setItem('theme-color', color);
    
    // Emit change
    this._themeSubject.next(color);
  }

  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  private lightenColor(color: string, percent: number): string {
    const rgb = this.hexToRgb(color);
    if (!rgb) return color;
    
    const r = Math.min(255, Math.floor(rgb.r + (255 - rgb.r) * percent / 100));
    const g = Math.min(255, Math.floor(rgb.g + (255 - rgb.g) * percent / 100));
    const b = Math.min(255, Math.floor(rgb.b + (255 - rgb.b) * percent / 100));
    
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  private darkenColor(color: string, percent: number): string {
    const rgb = this.hexToRgb(color);
    if (!rgb) return color;
    
    const r = Math.max(0, Math.floor(rgb.r * (1 - percent / 100)));
    const g = Math.max(0, Math.floor(rgb.g * (1 - percent / 100)));
    const b = Math.max(0, Math.floor(rgb.b * (1 - percent / 100)));
    
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  getCurrentTheme(): string {
    return this._themeSubject.value;
  }
}
