.landing-article {
    display: flex;
    flex-direction: column;
    height: 100%;

    &__image {
        display: block;
        border-radius: 12px;
        overflow: hidden;
        aspect-ratio: 16 / 9;
        background: #f3f5f8;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }
    }

    &__meta {
        margin-top: 10px;
        color: #697386;
        font-size: 0.875rem;
        min-height: 1.25rem;
        display: flex;
        align-items: center;
    }

    &__date {
        white-space: nowrap;
    }

    &__title {
        margin: 6px 0 0 0;
        font-size: 1rem;
        font-weight: 600;
        line-height: 1.4;
        flex: 1;

        a {
            color: #0b1526;
            text-decoration: none;
        }
    }

    &__more {
        margin-top: 8px;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        color: #0a66ff;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.95rem;
    }

    &__arrow {
        transition: transform 120ms ease;
    }

    &__more:hover .landing-article__arrow {
        transform: translateX(2px);
    }
}

.landing-article{
  background: #fff;
  border-radius: 16px;
  border: 1px solid rgba(2,18,43,.06);
  padding: 0;  
  position: relative;

  box-shadow:
    0 1px 2px rgba(2,18,43,.03), 
    0 4px 10px rgba(2,18,43,.05),
    0 18px 28px -10px rgba(2,18,43,.12);
  transition: transform .22s ease,
             box-shadow .28s cubic-bezier(.2,0,0,1),
             border-color .22s ease;
}

.landing-article:hover,
.landing-article:focus-within{
  transform: translateY(-4px);
  border-color: rgba(2,18,43,.08);
  box-shadow:
    0 2px 6px rgba(2,18,43,.05),
    0 10px 16px rgba(2,18,43,.06),
    0 28px 44px -12px rgba(2,18,43,.14);
  outline: none;
}

.landing-article__image{
  display: block;
  aspect-ratio: 16/9;
  background: #f3f5f8;
  border-radius: 16px 16px 0 0;
  overflow: hidden;

  img{
    width: 100%; height: 100%; object-fit: cover; display: block;
  }
}

/* --- Text paddings & link color --- */
.landing-article__meta,
.landing-article__title,
.landing-article__more{ padding: 0 16px; }

.landing-article__more{
  margin: 10px 0 14px;
  color: #0EA5E9;
  text-decoration: none; font-weight: 600;
}
.landing-article__more:hover{ text-decoration: underline; }
