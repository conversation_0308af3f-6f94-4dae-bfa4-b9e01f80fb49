import {
  HttpErrorRespo<PERSON>,
  HttpEvent,
  HttpH<PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
} from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { AuthenticationService } from "app/auth/service";
import { Observable, throwError } from "rxjs";
import { catchError, switchMap } from "rxjs/operators";
import { WebSocketService } from "../service/webSocket.service";

@Injectable()
export class JwtInterceptor implements HttpInterceptor {
  constructor(
    private _authenticationService: AuthenticationService,
    private webSocketService: WebSocketService,
    private router: Router
  ) {}

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    let accessToken = localStorage.getItem("token");
    let refreshToken = localStorage.getItem("refresh");

    // Không chặn request refresh token để tr<PERSON>h vòng lặp vô tận
    if (request.url.includes("/refresh")) {
      return next.handle(request);
    }

    if (accessToken) {
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
    }

    return next.handle(request).pipe(
      catchError((error) => {
        // console.log(error);
        if (error instanceof HttpErrorResponse && error.status === 401) {
          if (!refreshToken) {
            this.handleLogout();
            return throwError(() => error);
          }
          return this._authenticationService.refreshToken(refreshToken).pipe(
            switchMap((response) => {
              localStorage.setItem("token", response.access);
              this.webSocketService.reconnect();
              this._authenticationService.setTokenValue(response.access);

              const newRequest = request.clone({
                setHeaders: {
                  Authorization: `Bearer ${response.access}`,
                },
              });

              return next.handle(newRequest);
            }),
            catchError((refreshError) => {
              this.handleLogout();
              return throwError(() => refreshError);
            })
          );
        } else if (
          error instanceof HttpErrorResponse &&
          error.status == 503
          // (error.status === 502 || error.status === 504 || error.status === 0)
        ) {
          this.router.navigate(["/pages/authentication/maintain"]);
          return throwError(error.error);
        }
        // else if (
        //   error instanceof HttpErrorResponse &&
        //   error.status === 0 &&
        //   error.error instanceof ProgressEvent &&
        //   error.error.type === "error"
        // ) {
        //   // Xử lý lỗi CORS
        //   this.router.navigate(["/pages/authentication/maintain"]);
        //   return throwError(() => error);
        // }
        return throwError(error.error);
      })
    );
  }

  private handleLogout() {
    this._authenticationService.logout();
    localStorage.removeItem("token");
    localStorage.removeItem("refresh");
    this.router.navigate(["/pages/authentication/login-v2"]);
  }
}
