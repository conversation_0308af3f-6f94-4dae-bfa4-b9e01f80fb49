import { Injectable } from '@angular/core';
import * as d3 from 'd3';
import {
  GraphNode,
  GraphLink,
  ApiNode,
} from '../types/graph.types';
import {
  D3GraphNode,
  D3GraphLink,
  GraphRendererCallbacks,
} from '../models/graph-renderer.models';
import {
  NODE_COLORS,
  NODE_SIZES,
  CHART_CONFIG,
} from '../constants/graph.constants';
import { mapStrengthToColor } from '../helper/helper';

/**
 * Service for rendering graph using D3.js
 */
@Injectable({
  providedIn: 'root',
})
export class GraphRendererService {
  private readonly LINK_ARROW_LENGTH = 10;
  private readonly LINK_ARROW_PADDING = 6;
  private readonly DRAG_SMOOTHING_ALPHA = 0.2;

  private d3Svg?: d3.Selection<SVGSVGElement, unknown, null, undefined>;
  private d3RootGroup?: d3.Selection<SVGGElement, unknown, null, undefined>;
  private d3Simulation?: d3.Simulation<D3GraphNode, D3GraphLink>;
  private previousNodePositions: Map<string, { x: number; y: number }> = new Map();

  /**
   * Get current node positions from simulation before destroying
   */
  getCurrentNodePositions(): Map<string, { x: number; y: number }> {
    const positions = new Map<string, { x: number; y: number }>();
    if (this.d3Simulation) {
      const nodes = this.d3Simulation.nodes();
      nodes.forEach(node => {
        if (node.x != null && node.y != null) {
          positions.set(node.id, { x: node.x, y: node.y });
        }
      });
    }
    return positions;
  }

  /**
   * Render graph in container
   */
  renderGraph(
    container: HTMLElement,
    nodes: GraphNode[],
    links: GraphLink[],
    apiNodeMap: Map<string, ApiNode>,
    rootNodeId: string,
    d3Nodes: D3GraphNode[],
    d3Links: D3GraphLink[],
    callbacks?: GraphRendererCallbacks
  ): void {
    if (!nodes.length) {
      this.destroy();
      container.innerHTML = '';
      return;
    }

    // Save current node positions before destroying
    this.previousNodePositions = this.getCurrentNodePositions();

    this.destroy();
    container.innerHTML = '';

    const { width, height } = this.getContainerSize(container);

    const svg = d3
      .select(container)
      .append('svg')
      .attr('class', 'd3-svg')
      .attr('width', '100%')
      .attr('height', '100%')
      .attr('viewBox', `0 0 ${width} ${height}`)
      .attr('preserveAspectRatio', 'xMidYMid meet');

    this.d3Svg = svg;

    // Create arrow marker
    const defs = svg.append('defs');
    defs
      .append('marker')
      .attr('id', 'graph-link-arrow')
      .attr('markerUnits', 'userSpaceOnUse')
      .attr('viewBox', '0 -5 10 10')
      .attr('refX', this.LINK_ARROW_LENGTH / 2)
      .attr('refY', 0)
      .attr('markerWidth', this.LINK_ARROW_LENGTH)
      .attr('markerHeight', this.LINK_ARROW_LENGTH)
      .attr('orient', 'auto')
      .append('path')
      .attr('d', 'M0,-5L10,0L0,5Z')
      .attr('fill', '#666')
      .attr('stroke', 'none');

    const rootGroup = svg.append('g').attr('class', 'd3-graph-root');
    this.d3RootGroup = rootGroup;

    // Setup zoom
    const zoomBehavior = d3
      .zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.2, 5])
      .on('zoom', (event) => {
        rootGroup.attr('transform', event.transform.toString());
      });

    svg.call(zoomBehavior as any);

    // Render links
    const linkGroup = rootGroup.append('g').attr('class', 'graph-links');
    const linkSelection = linkGroup
      .selectAll<SVGGElement, D3GraphLink>('g')
      .data(d3Links, (d: any) => d.id)
      .join(
        (enter) => {
          const group = enter.append('g').attr('class', 'relationship');
          group
            .append('path')
            .attr('class', 'overlay')
            .attr('fill', 'none');
          group
            .append('path')
            .attr('class', 'outline')
            .attr('stroke', (d) =>
              d.__isBaiBo ? NODE_COLORS.ARTICLE : mapStrengthToColor(d.strength)
            )
            .attr('stroke-width', CHART_CONFIG.LINE_STYLE.WIDTH)
            .attr('stroke-dasharray', (d) => (d.__isBaiBo ? '4 2' : null))
            .attr('fill', 'none')
            .attr('stroke-opacity', (d) => d.opacity)
            .attr('opacity', (d) => d.opacity)
            .attr('marker-end', 'url(#graph-link-arrow)');
          return group;
        },
        (update) => update,
        (exit) => exit.remove()
      );

    // Render link labels
    const linkLabelGroup = rootGroup
      .append('g')
      .attr('class', 'graph-link-labels');
    const linkLabels = linkLabelGroup
      .selectAll<SVGGElement, D3GraphLink>('g')
      .data(
        d3Links.filter((link) => !!link.label),
        (d: any) => d.id
      )
      .join(
        (enter) => {
          const group = enter.append('g').attr('class', 'graph-link-label');
          group
            .append('rect')
            .attr('class', 'text-bg')
            .attr('fill', '#ffffff')
            .attr('fill-opacity', 1)
            .attr('rx', 2)
            .attr('ry', 2);
          group
            .append('text')
            .attr('class', 'text')
            .attr('fill', '#000000')
            .attr('font-size', '8px')
            .attr('pointer-events', 'none')
            .attr('text-anchor', 'middle')
            .attr('dy', '0.35em')
            .text((d) => d.label || '');
          return group;
        },
        (update) => update,
        (exit) => exit.remove()
      )
      .attr('opacity', (d) => d.opacity ?? 1);

    // Render nodes
    const nodeGroup = rootGroup.append('g').attr('class', 'graph-nodes');

    const dragBehaviour = d3
      .drag<SVGGElement, D3GraphNode>()
      .on('start', (event, d) => {
        if (!event.active && this.d3Simulation) {
          this.d3Simulation.alphaTarget(0.3).restart();
        }
        d.fx = d.x;
        d.fy = d.y;
      })
      .on('drag', (event, d) => {
        const currentFx = d.fx ?? d.x ?? event.x;
        const currentFy = d.fy ?? d.y ?? event.y;
        d.fx = currentFx + (event.x - currentFx) * this.DRAG_SMOOTHING_ALPHA;
        d.fy = currentFy + (event.y - currentFy) * this.DRAG_SMOOTHING_ALPHA;
        if (callbacks?.onNodeDrag) {
          callbacks.onNodeDrag(d.id, d.fx, d.fy);
        }
      })
      .on('end', (event) => {
        if (!event.active && this.d3Simulation) {
          this.d3Simulation.alphaTarget(0);
        }
      });

    const nodeSelection = nodeGroup
      .selectAll<SVGGElement, D3GraphNode>('g')
      .data(d3Nodes, (d: any) => d.id)
      .join('g')
      .attr('class', (d) =>
        `graph-node${d.isBlurred ? ' node-blurred' : ''}`
      )
      .style('cursor', (d) => (d.isBlurred ? 'not-allowed' : 'pointer'))
      .on('click', (event, d) => {
        if (d.isBlurred) {
          return;
        }
        const apiNode = apiNodeMap.get(d.id) ?? null;
        if (apiNode && callbacks?.onNodeClick) {
          callbacks.onNodeClick(d.id, apiNode);
        }
      })
      .on('contextmenu', (event, d) => {
        if (d.isBlurred) {
          return;
        }
        event.preventDefault();
        const apiNode = apiNodeMap.get(d.id) ?? null;
        if (apiNode && callbacks?.onNodeRightClick) {
          callbacks.onNodeRightClick(event as MouseEvent, d.id, apiNode);
        }
      })
      .on('dblclick', (event, d) => {
        if (d.isBlurred) {
          return;
        }
        event.preventDefault();
        event.stopPropagation();
        const apiNode = apiNodeMap.get(d.id) ?? null;
        if (apiNode && callbacks?.onNodeDoubleClick) {
          callbacks.onNodeDoubleClick(d.id, apiNode);
        }
      })
      .call(dragBehaviour);

    // Add ring layer
    nodeSelection
      .append('circle')
      .attr('class', 'ring')
      .attr('r', (d) => {
        const nodeRadius = (d.symbolSize || NODE_SIZES.REGULAR) / 2;
        return nodeRadius * 1.16;
      });

    // Add outline circle
    nodeSelection
      .append('circle')
      .attr('class', 'outline')
      .attr('r', (d) => (d.symbolSize || NODE_SIZES.REGULAR) / 2)
      .attr('fill', (d) => d.color || NODE_COLORS.DEFAULT)
      .attr('fill-opacity', (d) => d.opacity ?? 1)
      .attr('stroke', '#fff')
      .attr('stroke-width', 1.5)
      .classed('graph-node--blurred', (d) => !!d.isBlurred);

    // Add label
    const labelSelection = nodeSelection
      .append('text')
      .attr('class', 'graph-node-label')
      .attr('text-anchor', 'middle')
      .attr('fill', '#fff')
      .attr('font-size', 10)
      .attr('pointer-events', 'all');

    labelSelection.each((d, i, nodes) => {
      const textEl = d3.select(nodes[i]);
      this.renderMultilineLabel(textEl, d.label);
    });

    // Add tooltip
    nodeSelection.append('title').text((d) => {
      const apiNode = apiNodeMap.get(d.id) ?? null;
      if (!apiNode || d.isBlurred) {
        return '';
      }
      if (callbacks?.formatTooltip) {
        return callbacks.formatTooltip(d.id, apiNode, d.label);
      }
      return d.label;
    });

    // Initialize new nodes near their parent nodes before running simulation
    // This prevents edges from stretching too far when new nodes are added
    // First pass: find all new nodes (nodes without positions)
    const newNodes = new Set<D3GraphNode>();
    d3Nodes.forEach(node => {
      if (node.x == null || node.y == null) {
        newNodes.add(node);
      }
    });

    // Second pass: initialize new nodes near their first parent that has a position
    newNodes.forEach(newNode => {
      // Find the first link where this node is the target and source has a position
      const parentLink = d3Links.find(link => {
        const t = link.target as D3GraphNode;
        const s = link.source as D3GraphNode;
        return t && t.id === newNode.id && s && s.x != null && s.y != null;
      });

      if (parentLink) {
        const s = parentLink.source as D3GraphNode;
        // Initialize near parent with small random offset
        newNode.x = s.x! + (Math.random() * 30 - 15);
        newNode.y = s.y! + (Math.random() * 30 - 15);
      } else {
        // No parent with position found, use center
        newNode.x = width / 2;
        newNode.y = height / 2;
      }
    });

    // Ensure root node has a position
    const rootNode = d3Nodes.find(n => n.id === rootNodeId);
    if (rootNode && (rootNode.x == null || rootNode.y == null)) {
      rootNode.x = width / 2;
      rootNode.y = height / 2;
    }

    // Setup force simulation
    const simulation = d3
      .forceSimulation<D3GraphNode>(d3Nodes)
      .force(
        'link',
        d3
          .forceLink<D3GraphNode, D3GraphLink>(d3Links)
          .id((d: any) => d.id)
          .distance(CHART_CONFIG.FORCE.EDGE_LENGTH)
      )
      .force('charge', d3.forceManyBody().strength(-CHART_CONFIG.FORCE.REPULSION))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force(
        'collision',
        d3.forceCollide().radius(
          (d) => (d.symbolSize || NODE_SIZES.REGULAR) / 2 + 5
        )
      )
      .on('tick', () => {
        // Update link paths
        linkSelection.each((d, i, nodes) => {
          const group = d3.select(nodes[i]);
          const pathData = this.buildLinkPath(d);
          group
            .select<SVGPathElement>('path.outline')
            .attr('d', pathData)
            .attr('opacity', (d) => d.opacity ?? 1);
          group.select<SVGPathElement>('path.overlay').attr('d', pathData);
        });

        // Update link labels
        linkLabels.attr('transform', (d) => this.getLinkLabelTransform(d));
        linkLabels.attr('opacity', (d) => d.opacity ?? 1);
        linkLabels.each((d, i, nodes) => {
          const group = d3.select(nodes[i]);
          const textEl = group.select<SVGTextElement>('text.text');
          const rectEl = group.select<SVGRectElement>('rect.text-bg');
          const textNode = textEl.node();
          if (!textNode) {
            return;
          }
          const bbox = textNode.getBBox();
          const padding = 3;
          const labelWidth = bbox.width + padding * 2;
          const labelHeight = bbox.height + padding * 2;
          rectEl
            .attr('width', labelWidth)
            .attr('height', labelHeight)
            .attr('x', -labelWidth / 2)
            .attr('y', -labelHeight / 2);
        });

        // Update node positions
        nodeSelection.attr('transform', (d) => {
          const x = d.x ?? width / 2;
          const y = d.y ?? height / 2;
          return `translate(${x}, ${y})`;
        });
      });

    this.d3Simulation = simulation;
  }

  private renderMultilineLabel(
    textSelection: d3.Selection<SVGTextElement, D3GraphNode, null, undefined>,
    label: string
  ): void {
    const lines = (label || '').split('\n').filter((line) => line.trim().length > 0);
    if (!lines.length) {
      lines.push('');
    }

    textSelection.selectAll('tspan').remove();

    const lineHeight = 12;
    const totalHeight = lineHeight * (lines.length - 1);

    lines.forEach((line, index) => {
      textSelection
        .append('tspan')
        .attr('x', 0)
        .attr('dy', index === 0 ? -(totalHeight / 2) : lineHeight)
        .text(line);
    });
  }

  /**
   * Destroy the graph and clean up resources
   */
  destroy(): void {
    this.d3Simulation?.stop();
    this.d3Simulation = undefined;
    this.d3Svg?.remove();
    this.d3Svg = undefined;
    this.d3RootGroup = undefined;
  }

  /**
   * Build SVG path for a link
   */
  private buildLinkPath(link: D3GraphLink): string {
    const source = link.source as D3GraphNode;
    const target = link.target as D3GraphNode;
    if (!source || !target || source.x === undefined || target.x === undefined) {
      return '';
    }

    const x1 = source.x;
    const y1 = source.y ?? 0;
    const trimmed = this.getTrimmedTargetPoint(link);
    const x2 = trimmed.x;
    const y2 = trimmed.y;
    const curveness = link.__curveness ?? 0;

    if (!curveness) {
      return `M${x1},${y1} L${x2},${y2}`;
    }

    const midX = (x1 + x2) / 2;
    const midY = (y1 + y2) / 2;
    const dx = x2 - x1;
    const dy = y2 - y1;
    const distance = Math.sqrt(dx * dx + dy * dy) || 1;
    const maxOffset = Math.min(120, distance * 0.5);
    const offset = curveness * maxOffset;
    const perpX = (-dy / distance) * offset;
    const perpY = (dx / distance) * offset;
    const controlX = midX + perpX;
    const controlY = midY + perpY;
    return `M${x1},${y1} Q${controlX},${controlY} ${x2},${y2}`;
  }

  /**
   * Get transform string for link label
   */
  private getLinkLabelTransform(link: D3GraphLink): string {
    const source = link.source as D3GraphNode;
    const target = link.target as D3GraphNode;
    if (!source || !target || source.x === undefined || target.x === undefined) {
      return 'translate(0,0)';
    }

    const startX = source.x ?? 0;
    const startY = source.y ?? 0;
    const trimmed = this.getTrimmedTargetPoint(link);
    const curveness = link.__curveness ?? 0;

    let midX: number;
    let midY: number;
    let angle: number;
    let normalX: number;
    let normalY: number;

    if (curveness !== 0) {
      const x1 = startX;
      const y1 = startY;
      const x2 = trimmed.x;
      const y2 = trimmed.y;
      const dx = x2 - x1;
      const dy = y2 - y1;
      const distance = Math.sqrt(dx * dx + dy * dy) || 1;
      const maxOffset = Math.min(120, distance * 0.5);
      const offset = curveness * maxOffset;
      const perpX = (-dy / distance) * offset;
      const perpY = (dx / distance) * offset;
      const controlX = (x1 + x2) / 2 + perpX;
      const controlY = (y1 + y2) / 2 + perpY;

      const t = 0.5;
      const mt = 1 - t;
      midX = mt * mt * x1 + 2 * mt * t * controlX + t * t * x2;
      midY = mt * mt * y1 + 2 * mt * t * controlY + t * t * y2;

      const tangentX = 2 * mt * (controlX - x1) + 2 * t * (x2 - controlX);
      const tangentY = 2 * mt * (controlY - y1) + 2 * t * (y2 - controlY);
      const tangentLength = Math.sqrt(tangentX * tangentX + tangentY * tangentY) || 1;
      angle = (Math.atan2(tangentY, tangentX) * 180) / Math.PI;

      normalX = -tangentY / tangentLength;
      normalY = tangentX / tangentLength;
    } else {
      midX = (startX + trimmed.x) / 2;
      midY = (startY + trimmed.y) / 2;
      angle = (Math.atan2(trimmed.y - startY, trimmed.x - startX) * 180) / Math.PI;

      const dx = trimmed.x - startX;
      const dy = trimmed.y - startY;
      const distance = Math.sqrt(dx * dx + dy * dy) || 1;
      normalX = -dy / distance;
      normalY = dx / distance;
    }

    // Normalize angle to 0-360 range
    const normalizedAngle = ((angle % 360) + 360) % 360;
    
    // If angle is between 90 and 270 degrees, the label would be upside-down
    // So we flip it by rotating 180 degrees
    const mirror = normalizedAngle > 90 && normalizedAngle < 270;
    const offsetAmount = mirror ? 2 : -3;
    const adjustedOffset = curveness !== 0 ? offsetAmount * 0.3 : offsetAmount;

    const offsetX = normalX * adjustedOffset;
    const offsetY = normalY * adjustedOffset;
    // Rotate 180 degrees if label would be upside-down
    const rotation = mirror ? normalizedAngle + 180 : normalizedAngle;

    return `translate(${midX + offsetX}, ${midY + offsetY}) rotate(${rotation})`;
  }

  /**
   * Get trimmed target point for link (accounting for node radius)
   */
  private getTrimmedTargetPoint(
    link: D3GraphLink,
    extraOffset: number = 0
  ): { x: number; y: number; dirX: number; dirY: number; angle: number } {
    const source = link.source as D3GraphNode;
    const target = link.target as D3GraphNode;
    const x1 = source?.x ?? 0;
    const y1 = source?.y ?? 0;
    const x2 = target?.x ?? 0;
    const y2 = target?.y ?? 0;
    const dx = x2 - x1;
    const dy = y2 - y1;
    const distance = Math.sqrt(dx * dx + dy * dy) || 1;
    const dirX = dx / distance;
    const dirY = dy / distance;
    const angle = Math.atan2(dy, dx);

    const nodeRadius = (target?.symbolSize || NODE_SIZES.REGULAR) / 2;
    const trim = nodeRadius + this.LINK_ARROW_PADDING + extraOffset;
    const effectiveTrim = Math.min(trim, distance * 0.9);
    const trimmedX = x2 - dirX * effectiveTrim;
    const trimmedY = y2 - dirY * effectiveTrim;

    return {
      x: trimmedX,
      y: trimmedY,
      dirX,
      dirY,
      angle,
    };
  }

  /**
   * Get container size
   */
  private getContainerSize(container: HTMLElement): { width: number; height: number } {
    const rect = container.getBoundingClientRect();
    return {
      width: rect.width || 800,
      height: rect.height || 600,
    };
  }
}

