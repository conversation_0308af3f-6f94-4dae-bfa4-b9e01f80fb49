import { Component, ElementRef, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';

type ChatRole = 'user' | 'assistant' | 'system';

interface ChatMessage {
    id: string;
    role: ChatRole;
    content: string;
    createdAt: number;
}

@Component({
    selector: 'app-landing-chatbot',
    templateUrl: './landing-chatbot.component.html',
    styleUrls: ['./landing-chatbot.component.scss']
})
export class LandingChatbotComponent implements OnInit, OnDestroy {
    private readonly LOGIN_URL = '/pages/authentication/login-v2';
    form!: FormGroup;
    messages: ChatMessage[] = [];
    loading = false;
    // hasSessionStarted = false; 
    private subs: Subscription[] = [];

    @ViewChild('scrollBox') scrollBox!: ElementRef<HTMLDivElement>;
    @ViewChild('inputEl') inputEl!: ElementRef<HTMLInputElement>;

    suggestions = [
      'Quản lý tài liệu',
      'Rà soát mâu thuẫn',
      'So sánh điều khoản',
      'Tra cứu tài liệu',
    ];

    // ==== Placeholder động ====
    placeholderSamples: string[] = [
      'Công ty đơn phương chấm dứt hợp đồng có đúng luật không?',
      'Điều kiện rút BHXH một lần là gì?',
      'Đất nông nghiệp có được chuyển đổi sang đất ở không?',
    ];
    placeholderIndex = 0;
    currentPlaceholder = this.placeholderSamples[0];
    placeholderAnimate = false;
    isFadingOut = false;
    private placeholderTimer: any;
    private readonly PLACEHOLDER_DELAY = 3000;

  constructor(private fb: FormBuilder, private http: HttpClient, private router: Router) {}

    ngOnInit(): void {
        this.form = this.fb.group({
        prompt: ['', [Validators.required, Validators.minLength(1)]]
    });
    this.startPlaceholderLoop();

    // Tin nhắn chào
    // this.messages.push({
    //   id: crypto.randomUUID(),
    //   role: 'assistant',
    //   content: 'Tôi có thể giúp gì cho bạn?',
    //   createdAt: Date.now()
    // });
  }

    ngOnDestroy(): void {
        this.subs.forEach(s => s.unsubscribe());
        this.stopPlaceholderLoop();
    }

    // private startChatSession() {
    //     if (!this.hasSessionStarted) {
    //     this.hasSessionStarted = true;
    //     this.messages.push({
    //         id: crypto.randomUUID(), role: 'assistant',
    //         content: 'Tôi có thể giúp gì cho bạn?', createdAt: Date.now()
    //     });
    //     }
    // }

    pickSuggestion(text: string) {
        this.form.patchValue({ prompt: text });
        // this.startChatSession();
        // this.focusInput();
    }

    // onInputFocus() { this.startChatSession(); }
    onInputChange() {
        const text = String(this.form.value?.prompt || '').trim();

        if (text) {
            // người dùng đã gõ → dừng loop, ẩn placeholder
            this.stopPlaceholderLoop();
        } else {
            // xóa hết text → chạy lại loop
            if (!this.placeholderTimer) {
            this.startPlaceholderLoop();
            }
        }
    }
    private isUserTyping(): boolean {
        return !!String(this.form?.value?.prompt || '').trim();
        }

    private startPlaceholderLoop() {
        this.stopPlaceholderLoop();
        if (!this.placeholderSamples.length) return;

        const scheduleNext = () => {
            if (this.isUserTyping()) {
            this.placeholderTimer = null;
            return;
            }
            // kích hoạt fade-out (CSS lo phần mượt)
            this.isFadingOut = true;
        };

        this.placeholderTimer = setTimeout(scheduleNext, this.PLACEHOLDER_DELAY);
    }

    private stopPlaceholderLoop() {
        if (this.placeholderTimer) {
            clearTimeout(this.placeholderTimer);
            this.placeholderTimer = null;
        }
        this.isFadingOut = false;
    }
    onPlaceholderTransitionEnd(ev: TransitionEvent) {
    // chỉ care opacity, tránh bị gọi 2 lần (opacity + transform)
    if (ev.propertyName !== 'opacity') return;

    if (this.isFadingOut) {
        // >>> VỪA fade-out XONG: text cũ đã invisible
        // đổi sang câu tiếp theo
        this.placeholderIndex =
        (this.placeholderIndex + 1) % this.placeholderSamples.length;
        this.currentPlaceholder = this.placeholderSamples[this.placeholderIndex];

        // bắt đầu fade-in
        this.isFadingOut = false;
    } else {
        // >>> VỪA fade-in XONG: text mới đã hiện rõ
        if (!this.isUserTyping()) {
        // chờ thêm 3s rồi lại fade-out
        this.placeholderTimer = setTimeout(() => {
            this.isFadingOut = true;
        }, this.PLACEHOLDER_DELAY);
        } else {
        this.stopPlaceholderLoop();
        }
    }
    }

    // send(): void {
    //     if (this.form.invalid || this.loading) return;
    //     this.startChatSession();

    //     const content = this.form.value.prompt.trim();
    //     if (!content) return;

    //     const userMsg: ChatMessage = {
    //     id: crypto.randomUUID(),
    //     role: 'user',
    //     content,
    //     createdAt: Date.now()
    //     };
    //     this.messages.push(userMsg);
    //     this.form.reset();
    //     this.autoScroll();

    //     // Hiển thị placeholder trong khi gọi API
    //     const pendingId = crypto.randomUUID();
    //     this.messages.push({
    //     id: pendingId,
    //     role: 'assistant',
    //     content: 'Đang xử lý…',
    //     createdAt: Date.now()
    //     });
    //     this.loading = true;

    //     const sub = this.http.post<{ reply: string }>(
    //     '/api/chat',
    //     { message: content, source: 'landing' }
    //     ).subscribe({
    //     next: (res) => {
    //         this.replacePending(pendingId, res.reply || 'Em đã nhận được câu hỏi của anh.');
    //         this.loading = false;
    //         this.autoScroll();
    //     },
    //     error: () => {
    //         this.replacePending(pendingId, 'Xin lỗi, hệ thống đang bận. Anh thử lại giúp em nhé.');
    //         this.loading = false;
    //         this.autoScroll();
    //     }
    //     });

    //     this.subs.push(sub);
    // }
    send(): void {
        const content = String(this.form.value?.prompt || '').trim();
        this.router.navigateByUrl(this.LOGIN_URL);
    }

    onEnter(e: KeyboardEvent) {
        if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.send();
        }
    }

    private replacePending(id: string, content: string) {
        const idx = this.messages.findIndex(m => m.id === id);
        if (idx > -1) this.messages[idx] = { ...this.messages[idx], content };
    }

    private autoScroll() {
        setTimeout(() => {
        if (this.scrollBox) this.scrollBox.nativeElement.scrollTop = this.scrollBox.nativeElement.scrollHeight;
        }, 0);
    }

    private focusInput() {
        setTimeout(() => this.inputEl?.nativeElement.focus(), 0);
    }
}
