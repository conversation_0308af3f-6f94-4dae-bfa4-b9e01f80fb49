:host {
  display: block;
  width: 100%;
}

.workspace-feature-cards {
  /* ===== Grid các card ===== */
  &__container {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 24px;
    margin-top: 2rem;
    min-width: 0;
  }

  /* ===== Card ===== */
  &__card {
    background: #f5f5f5;                 /* vùng bao quanh nội dung */
    border-radius: 12px;
    padding: 1.25rem 1.5rem 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    min-width: 0;
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    }
  }

  /* ===== Badge số thứ tự ===== */
  &__card-step {
    width: 34px;
    height: 34px;
    border-radius: 999px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
    color: #008fe3;
    background: #ffffff;
    border: 1px solid #d7e8f8;
    margin-bottom: 1rem;
  }

  /* ===== Nội dung text ===== */
  &__card-content {
    margin-bottom: 1.25rem;
  }

  &__card-title {
    font-size: 1.05rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333333;
  }

  &__card-description {
    color: #666666;
    line-height: 1.5;
  }

  /* ===== Ảnh ===== */
  &__card-image {
    margin-bottom: 0;
  }

  &__card-img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    display: block;
  }
}

/* ====== Tablet: 2 cột ====== */
@media (max-width: 992px) {
  .workspace-feature-cards__container {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* ====== Mobile: 1 cột ====== */
@media (max-width: 576px) {
  .workspace-feature-cards__container {
    grid-template-columns: 1fr;
  }
}
