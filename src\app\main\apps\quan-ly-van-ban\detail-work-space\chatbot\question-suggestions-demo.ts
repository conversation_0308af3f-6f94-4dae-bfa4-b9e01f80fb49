// Demo data cho tính năng Question Suggestions
// File này có thể được sử dụng để test tính năng khi backend chưa sẵn sàng

export const MOCK_QUESTION_SUGGESTIONS = [
  {
    id: "1",
    question: "<PERSON><PERSON>ều kiện để được ly hôn đơn phương là gì?"
  },
  {
    id: "2", 
    question: "Quy định về quyền nuôi con sau khi ly hôn như thế nào?"
  },
  {
    id: "3",
    question: "Thủ tục chia tài sản chung khi ly hôn được thực hiện ra sao?"
  },
  {
    id: "4",
    question: "<PERSON><PERSON> thể ly hôn khi một bên không đồng ý không?"
  },
  {
    id: "5",
    question: "Thời gian xử lý hồ sơ ly hôn mất bao lâu?"
  }
];

// Hàm để simulate API call với delay
export function mockInsertQuestionSuggestion(question: string, workspace_id: string): Promise<any> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log(`Mock: Inserted question "${question}" for workspace ${workspace_id}`);
      resolve({ success: true, id: Date.now().toString() });
    }, 500);
  });
}

export function mockSearchQuestionSuggestions(workspace_id: string): Promise<any[]> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log(`Mock: Searching suggestions for workspace ${workspace_id}`);
      // Trả về random 3-5 câu hỏi từ mock data
      const shuffled = [...MOCK_QUESTION_SUGGESTIONS].sort(() => 0.5 - Math.random());
      const count = Math.floor(Math.random() * 3) + 3; // 3-5 items
      resolve(shuffled.slice(0, count));
    }, 300);
  });
}

// Hướng dẫn sử dụng mock data:
// 1. Import các hàm mock vào chatbot.service.ts
// 2. Thay thế HTTP calls bằng mock functions khi cần test
// 3. Ví dụ:
/*
// Trong chatbot.service.ts
import { mockInsertQuestionSuggestion, mockSearchQuestionSuggestions } from './question-suggestions-demo';

// Thay vì:
// return this.http.post(...)

// Sử dụng:
// return from(mockInsertQuestionSuggestion(question, workspace_id));
*/
