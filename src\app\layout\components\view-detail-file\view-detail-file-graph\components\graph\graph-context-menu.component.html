<div
  class="context-menu"
  *ngIf="visible"
  [style.top.px]="position.y"
  [style.left.px]="position.x"
  (click)="onMenuClick($event)"
>
  <ul>
    <li 
      class="has-submenu"
      (mouseenter)="onSubmenuMouseEnter()"
      (mouseleave)="onSubmenuMouseLeave()"
    >
      <div class="menu-item-wrapper" (click)="onSubmenuToggleClick($event)">
        <span class="menu-item-content">
          <span data-feather="maximize-2" class="mr-25"></span> Mở rộng
        </span>
        <span 
          class="submenu-trigger"
          data-feather="chevron-right"
          (click)="onSubmenuToggleClick($event)"
        ></span>
      </div>
      
      <!-- Submenu -->
      <ul class="submenu" *ngIf="showExpandSubmenu">
        <li (click)="onExpandWithDefault($event)">
          <span><PERSON> l<PERSON><PERSON> đồ mặc định</span>
        </li>
        <li (click)="onExpandWithConditions($event)">
          <span><PERSON> đ<PERSON> ki<PERSON></span>
        </li>
      </ul>
    </li>
    <li *ngIf="menuItem?.isRoot" (click)="onRestoreNode($event)">
      <span data-feather="refresh-cw" class="mr-25"></span> Khôi phục
    </li>
    <li *ngIf="!menuItem?.isRoot" (click)="onCollapseNode($event)">
      <span data-feather="eye-off" class="mr-25"></span> Ẩn
    </li>
  </ul>
</div>

