<div *ngIf="!isFullTableView" class="graph-container">
  <!-- Graph Visualization Component -->
  <app-graph-visualization
    [nodes]="nodes"
    [links]="links"
    [apiNodeMap]="apiNodeMap"
    [rootNodeId]="rootNodeId"
    [isLoading]="isLoadingGraph || isLoadingGraphExternal"
    [d3Nodes]="d3Nodes"
    [d3Links]="d3Links"
    [callbacks]="graphCallbacks"
  ></app-graph-visualization>

  <!-- Document Details Panel Component -->
  <app-document-details-panel
    [visible]="showDocumentTable"
    [dataFile]="dataFile"
    [isClauseNode]="isClauseNode"
    [clauseContent]="clauseContent"
    [isLoadingClauseContent]="isLoadingClauseContent"
    [hasClauseContentError]="hasClauseContentError"
    [typeDocument]="typeDocument"
    (close)="onCloseDocumentTable()"
  ></app-document-details-panel>

  <!-- Document List Panel Component -->
  <app-document-list-panel
    [expanded]="documentListExpanded"
    [documentList]="documentList"
    [documentListSearch]="documentListSearch"
    [selectAllDocuments]="selectAllDocuments"
    [highlightedDocumentId]="highlightedDocumentId"
    [activeDocumentTab]="activeDocumentTab"
    [formState]="formState"
    [boLocMoiQuanHeOptions]="boLocMoiQuanHeOptions"
    [coQuanBanHanhOptions]="coQuanBanHanhOptions"
    [boLocLoaiVanBanOptions]="boLocLoaiVanBanOptions"
    [tinhTrangHieuLucOptions]="tinhTrangHieuLucOptions"
    [customYearOptions]="customYearOptions"
    (toggle)="toggleDocumentList()"
    (searchChange)="documentListSearch = $event; onDocumentListSearchChange()"
    (selectAllChange)="onSelectAllDocumentsChange($any({ target: { checked: $event } }))"
    (documentSelectChange)="onDocumentSelectChange($any({ target: { checked: $event.selected } }), $event.item)"
    (toggleFullTable)="toggleFullDocumentTable()"
    (viewModeChange)="onViewModeChange($event)"
    (moiQuanHeSelectionChange)="onMoiQuanHeSelectionChange($event)"
    (coQuanBanHanhSelectionChange)="onCoQuanBanHanhSelectionChange($event)"
    (loaiVanBanSelectionChange)="onLoaiVanBanSelectionChange($event)"
    (trangThaiHieuLucSelectionChange)="onTrangThaiHieuLucSelectionChange($event)"
    (yearChange)="changeYear($event)"
    (dateFilterModeChange)="setDateFilterMode($event.mode, $event.event)"
    (activeTabChange)="activeDocumentTab = $event"
    (documentHover)="onDocumentHover($event)"
  ></app-document-list-panel>
</div>

<!-- Document Table Component -->
<app-document-table
  *ngIf="isFullTableView"
  [documentList]="documentList"
  [fullTableSearch]="fullTableSearch"
  [page]="page"
  [pageSize]="pageSize"
  [selectedTableRows]="selectedTableRows"
  [highlightedDocumentId]="highlightedDocumentId"
  (toggleView)="toggleFullDocumentTable()"
  (searchChange)="fullTableSearch = $event; onFullTableSearchChange()"
  (pageChange)="onPageChange($event)"
  (tableSelect)="onTableSelect($event)"
  (tableActivate)="onTableActivate($event)"
  (headerCheckboxChange)="onHeaderCheckboxChange($any({ target: { checked: $event } }))"
></app-document-table>

<!-- Selection bar for saving documents (applies to both views) -->
<div class="save-file" *ngIf="selectedFiles.length > 0">
  <div
    class="selection-bar p-1 d-flex align-items-center justify-content-between"
  >
    <button
      class="close-btn mr-1"
      (click)="clearSelectedDocuments()"
      aria-label="Bỏ chọn tất cả"
    >
      &times;
    </button>
    <span class="height-30px mx-1"></span>
    <span class="selected-text"
      >Đã chọn {{ selectedFiles.length }} tài liệu</span
    >
    <span class="height-30px mx-1"></span>
    <button
      class="save-btn d-flex align-items-center"
      (click)="saveHistoryFiles()"
      [disabled]="isSavingFiles"
    >
      <ng-container *ngIf="!isSavingFiles; else savingSpinner">
        <img src="assets/images/icons/folder-star.svg" alt="folder-star" />
      </ng-container>
      <ng-template #savingSpinner>
        <span
          class="spinner-border spinner-border-sm text-primary"
          role="status"
        >
          <span class="sr-only">Loading...</span>
        </span>
      </ng-template>
      <span class="ms-2">{{ isSavingFiles ? 'Đang lưu' : 'Lưu tài liệu' }}</span>
    </button>
  </div>
</div>

<!-- Context Menu Component -->
<app-graph-context-menu
  [visible]="contextMenuVisible"
  [position]="contextMenuPosition"
  [menuItem]="contextMenuItem"
  [showExpandSubmenu]="showExpandSubmenu"
  (expandWithDefault)="onExpandNodeWithDefaultSchema()"
  (expandWithConditions)="openExpansionModal()"
  (restoreNode)="onRestoreNode()"
  (collapseNode)="onCollapseNode()"
  (toggleExpandSubmenu)="toggleExpandSubmenu($event)"
  (close)="closeContextMenu()"
></app-graph-context-menu>

<!-- Expansion Modal Component -->
<app-expansion-modal
  [visible]="showExpansionModal"
  [formState]="modalFormState"
  [boLocMoiQuanHeOptions]="fullBoLocMoiQuanHeOptions"
  [coQuanBanHanhOptions]="fullCoQuanBanHanhOptions"
  [boLocLoaiVanBanOptions]="fullBoLocLoaiVanBanOptions"
  [tinhTrangHieuLucOptions]="fullTinhTrangHieuLucOptions"
  [customYearOptions]="customYearOptions"
  (close)="closeExpansionModal()"
  (submit)="onSubmitModalExpansion()"
  (yearChange)="changeYear($event, true)"
></app-expansion-modal>
