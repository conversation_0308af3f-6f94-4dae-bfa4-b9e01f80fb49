import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { UserProfileComponent } from "./user-profile.component";
import { ReactiveFormsModule } from "@angular/forms";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { NgSelectModule } from "@ng-select/ng-select";
import { Ng2FlatpickrModule } from "ng2-flatpickr";
import { PipeModule } from "app/layout/components/pipe/pipe.module";

@NgModule({
  declarations: [UserProfileComponent],
  imports: [
    CommonModule,
    NgbModule,
    ReactiveFormsModule,
    NgSelectModule,
    Ng2FlatpickrModule,
    PipeModule,
  ],
  exports: [UserProfileComponent],
})
export class UserProfileModule {}
