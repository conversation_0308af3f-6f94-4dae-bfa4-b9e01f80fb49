import { Injectable } from '@angular/core';
import {
  GraphFormState,
  ApiNode,
  GraphNode,
  GraphLink,
  DateFilterMode,
} from '../types/graph.types';
import { GraphCacheService } from './graph-cache.service';
import { GraphApiResponse } from '../types/graph.types';

export interface NodeFilterContext {
  hasAuthorityFilter: boolean;
  normalizedSelectedAuthorities: string[];
  hasTypeFilter: boolean;
  normalizedSelectedTypes: string[];
  hasStatusFilter: boolean;
  normalizedSelectedStatuses: string[];
  hasDateFilter: boolean;
  dateFilterMode: DateFilterMode | null | undefined;
  dateFilterFrom: number | null | undefined;
  dateFilterTo: number | null | undefined;
  hasRelTypeFilter: boolean;
  selectedRelTypes: string[];
  nodesReachableViaRelType: Set<string> | null;
}

export interface NodeVisualState {
  isBlurred: boolean;
  opacity: number;
}

/**
 * Service for filtering graph nodes and links
 */
@Injectable({
  providedIn: 'root',
})
export class GraphFilterService {
  constructor(private cacheService: GraphCacheService) {}

  /**
   * Build filter context from form state
   */
  buildNodeFilterContext(
    formState: GraphFormState | null,
    rootNodeId: string,
    links: GraphLink[]
  ): NodeFilterContext {
    const selectedAuthorities = formState?.selectedCoQuanBanHanh || [];
    const normalizedSelectedAuthorities = selectedAuthorities.map((a) =>
      (a || '').trim()
    );

    const selectedTypes = formState?.selectedBoLocLoaiVanBan || [];
    const normalizedSelectedTypes = selectedTypes.map((t) => (t || '').trim());

    const selectedStatuses = formState?.selectedTinhTrangHieuLuc || [];
    const normalizedSelectedStatuses = selectedStatuses.map((s) =>
      (s || '').trim()
    );

    const selectedRelTypes = formState?.selectedBoLocMoiQuanHe || [];

    const dateFilterMode = formState?.dateFilterMode;
    const dateFilterFrom = formState?.dateFilterFrom;
    const dateFilterTo = formState?.dateFilterTo;

    const hasAuthorityFilter = normalizedSelectedAuthorities.length > 0;
    const hasTypeFilter = normalizedSelectedTypes.length > 0;
    const hasStatusFilter = normalizedSelectedStatuses.length > 0;
    const hasRelTypeFilter = selectedRelTypes.length > 0;
    const hasDateFilter =
      dateFilterMode !== null &&
      dateFilterMode !== undefined &&
      dateFilterFrom !== null &&
      dateFilterTo !== null;

    let nodesReachableViaRelType: Set<string> | null = null;
    if (hasRelTypeFilter && rootNodeId) {
      const reachableSets = selectedRelTypes.map((relType) =>
        this.getReachableNodesForRelationshipType(relType, rootNodeId, links)
      );

      if (reachableSets.length > 0) {
        // OR logic: include nodes reachable via ANY selected relationship type
        nodesReachableViaRelType = new Set<string>();
        reachableSets.forEach((set) => {
          set.forEach((nodeId) => nodesReachableViaRelType!.add(nodeId));
        });
      }
    }

    return {
      hasAuthorityFilter,
      normalizedSelectedAuthorities,
      hasTypeFilter,
      normalizedSelectedTypes,
      hasStatusFilter,
      normalizedSelectedStatuses,
      hasDateFilter,
      dateFilterMode,
      dateFilterFrom,
      dateFilterTo,
      hasRelTypeFilter,
      selectedRelTypes,
      nodesReachableViaRelType,
    };
  }

  /**
   * Get visual state for a node based on filters
   */
  getNodeVisualState(
    nodeId: string,
    apiNode: ApiNode | null,
    context: NodeFilterContext,
    rootNodeId: string,
    findApiNodeById: (id: string) => ApiNode | null
  ): NodeVisualState {
    if (nodeId === rootNodeId) {
      return { isBlurred: false, opacity: 1 };
    }

    const {
      hasAuthorityFilter,
      normalizedSelectedAuthorities,
      hasTypeFilter,
      normalizedSelectedTypes,
      hasStatusFilter,
      normalizedSelectedStatuses,
      hasDateFilter,
      dateFilterMode,
      dateFilterFrom,
      dateFilterTo,
      hasRelTypeFilter,
      nodesReachableViaRelType,
    } = context;

    const nodeData = apiNode ?? findApiNodeById(nodeId);

    let isBlurred = false;
    let nodeOpacity = 1;

    if (hasAuthorityFilter) {
      const coQuanBanHanh =
        (nodeData?.thuoc_tinh?.co_quan_ban_hanh || '').trim();
      const matchesAuthority = this.matchesAllSelections(
        coQuanBanHanh,
        normalizedSelectedAuthorities
      );
      if (!matchesAuthority) {
        isBlurred = true;
        nodeOpacity = 0.1;
      }
    }

    if (hasTypeFilter && !isBlurred) {
      const loaiVanBan = (nodeData?.thuoc_tinh?.loai_van_ban || '').trim();
      const matchesType = this.matchesAllSelections(
        loaiVanBan,
        normalizedSelectedTypes
      );
      if (!matchesType) {
        isBlurred = true;
        nodeOpacity = 0.1;
      }
    }

    if (hasStatusFilter && !isBlurred) {
      const tinhTrangHieuLuc = (
        nodeData?.thuoc_tinh?.tinh_trang_hieu_luc || ''
      ).trim();
      const matchesStatus = this.matchesAllSelections(
        tinhTrangHieuLuc,
        normalizedSelectedStatuses
      );
      if (!matchesStatus) {
        isBlurred = true;
        nodeOpacity = 0.1;
      }
    }

    if (
      hasDateFilter &&
      !isBlurred &&
      dateFilterFrom !== null &&
      dateFilterTo !== null
    ) {
      const dateString =
        dateFilterMode === 'ban_hanh'
          ? nodeData?.thuoc_tinh?.ngay_ban_hanh || ''
          : nodeData?.thuoc_tinh?.ngay_co_hieu_luc || '';
      const matchesDate = this.isDateInRange(
        dateString,
        dateFilterFrom,
        dateFilterTo
      );
      if (!matchesDate) {
        isBlurred = true;
        nodeOpacity = 0.1;
      }
    }

    if (hasRelTypeFilter && !isBlurred) {
      if (!nodesReachableViaRelType || !nodesReachableViaRelType.has(nodeId)) {
        isBlurred = true;
        nodeOpacity = 0.1;
      }
    }

    return { isBlurred, opacity: nodeOpacity };
  }

  /**
   * Build set of active node IDs that pass all filters
   */
  buildActiveNodeIdsForFilters(
    nodes: GraphNode[],
    apiNodeMap: Map<string, ApiNode>,
    context: NodeFilterContext,
    rootNodeId: string,
    findApiNodeById: (id: string) => ApiNode | null
  ): Set<string> {
    const {
      hasAuthorityFilter,
      normalizedSelectedAuthorities,
      hasTypeFilter,
      normalizedSelectedTypes,
      hasStatusFilter,
      normalizedSelectedStatuses,
      hasDateFilter,
      dateFilterMode,
      dateFilterFrom,
      dateFilterTo,
    } = context;

    const activeNodeIds = new Set<string>();

    const requiresFiltering =
      hasAuthorityFilter || hasTypeFilter || hasStatusFilter || hasDateFilter;

    if (requiresFiltering) {
      nodes.forEach((node) => {
        const apiNode = apiNodeMap.get(node.id) ?? findApiNodeById(node.id);
        let nodeMatches = true;

        if (!apiNode) {
          nodeMatches = false;
        } else {
          if (hasAuthorityFilter) {
            const coQuanBanHanh =
              (apiNode.thuoc_tinh?.co_quan_ban_hanh || '').trim();
            if (
              !this.matchesAllSelections(
                coQuanBanHanh,
                normalizedSelectedAuthorities
              )
            ) {
              nodeMatches = false;
            }
          }

          if (hasTypeFilter && nodeMatches) {
            const loaiVanBan = (apiNode.thuoc_tinh?.loai_van_ban || '').trim();
            if (
              !this.matchesAllSelections(loaiVanBan, normalizedSelectedTypes)
            ) {
              nodeMatches = false;
            }
          }

          if (hasStatusFilter && nodeMatches) {
            const tinhTrangHieuLuc = (
              apiNode.thuoc_tinh?.tinh_trang_hieu_luc || ''
            ).trim();
            if (
              !this.matchesAllSelections(
                tinhTrangHieuLuc,
                normalizedSelectedStatuses
              )
            ) {
              nodeMatches = false;
            }
          }

          if (
            hasDateFilter &&
            nodeMatches &&
            dateFilterFrom !== null &&
            dateFilterTo !== null
          ) {
            const dateString =
              dateFilterMode === 'ban_hanh'
                ? apiNode.thuoc_tinh?.ngay_ban_hanh || ''
                : apiNode.thuoc_tinh?.ngay_co_hieu_luc || '';
            if (!this.isDateInRange(dateString, dateFilterFrom, dateFilterTo)) {
              nodeMatches = false;
            }
          }
        }

        if (nodeMatches) {
          activeNodeIds.add(node.id);
        }
      });
    }

    if (rootNodeId) {
      activeNodeIds.add(rootNodeId);
    }

    return activeNodeIds;
  }

  /**
   * Determine if a link should be highlighted based on filters
   */
  shouldHighlightLink(
    sourceId: string,
    targetId: string,
    relationshipType: string | undefined,
    context: NodeFilterContext,
    activeNodeIds: Set<string>
  ): boolean {
    const {
      hasRelTypeFilter,
      selectedRelTypes,
      hasAuthorityFilter,
      hasTypeFilter,
      hasStatusFilter,
      hasDateFilter,
      nodesReachableViaRelType,
    } = context;

    let shouldHighlightByRelType = true;
    if (hasRelTypeFilter) {
      shouldHighlightByRelType =
        !!relationshipType && selectedRelTypes.includes(relationshipType);
    }

    let shouldHighlightByNodeFilter = true;
    if (
      hasAuthorityFilter ||
      hasTypeFilter ||
      hasStatusFilter ||
      hasDateFilter
    ) {
      const sourceIsActive = activeNodeIds.has(sourceId);
      const targetIsActive = activeNodeIds.has(targetId);
      shouldHighlightByNodeFilter = sourceIsActive && targetIsActive;
    }

    let shouldHighlightByRelTypeNodeFilter = true;
    if (hasRelTypeFilter) {
      const reachable = nodesReachableViaRelType;
      const sourceIsReachable = reachable?.has(sourceId);
      const targetIsReachable = reachable?.has(targetId);
      shouldHighlightByRelTypeNodeFilter =
        !!sourceIsReachable && !!targetIsReachable;
    }

    return (
      shouldHighlightByRelType &&
      shouldHighlightByNodeFilter &&
      shouldHighlightByRelTypeNodeFilter
    );
  }

  /**
   * Get reachable nodes for a specific relationship type
   */
  private getReachableNodesForRelationshipType(
    relType: string,
    rootNodeId: string,
    links: GraphLink[]
  ): Set<string> {
    const reachable = new Set<string>();
    if (!rootNodeId) {
      return reachable;
    }

    const adjacencyMap = new Map<string, Set<string>>();
    links.forEach((link) => {
      if (link.__relationshipType === relType) {
        if (!adjacencyMap.has(link.source)) {
          adjacencyMap.set(link.source, new Set());
        }
        adjacencyMap.get(link.source)!.add(link.target);

        if (!adjacencyMap.has(link.target)) {
          adjacencyMap.set(link.target, new Set());
        }
        adjacencyMap.get(link.target)!.add(link.source);
      }
    });

    const visited = new Set<string>();
    const queue: string[] = [rootNodeId];

    while (queue.length > 0) {
      const currentNodeId = queue.shift()!;
      if (visited.has(currentNodeId)) {
        continue;
      }
      visited.add(currentNodeId);

      const neighbors = adjacencyMap.get(currentNodeId);
      if (neighbors) {
        neighbors.forEach((neighborId) => {
          if (!visited.has(neighborId)) {
            queue.push(neighborId);
          }
        });
      }
    }

    if (!visited.has(rootNodeId)) {
      visited.add(rootNodeId);
    }

    return visited;
  }

  /**
   * Check if a value matches ANY selection (OR logic)
   */
  private matchesAllSelections(
    value: string,
    normalizedSelections: string[]
  ): boolean {
    if (!normalizedSelections.length) {
      return true;
    }
    const normalizedValue = (value || '').trim();
    return normalizedSelections.some((selected) => selected === normalizedValue);
  }

  /**
   * Check if a date string falls within the specified year range
   */
  isDateInRange(dateString: string, fromTimestamp: number, toTimestamp: number): boolean {
    if (!dateString) return false;

    try {
      const date = new Date(dateString);
      const time = date.getTime();
      if (isNaN(time)) return false;

      return time >= fromTimestamp && time <= toTimestamp;
    } catch {
      return false;
    }
  }
}

