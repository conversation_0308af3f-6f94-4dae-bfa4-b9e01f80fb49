import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "environments/environment";
import { Observable, BehaviorSubject, of } from "rxjs";
import { map, tap, shareReplay, finalize } from "rxjs/operators";

export type CmsRole = 'WRITER' | 'REVIEWER' | 'ADMIN';
type ID = number | string;

@Injectable({
  providedIn: "root",
})
export class CmsService {
  private readonly BASE = `${environment.apiUrl}/cms`;
  private url = environment.apiUrl;
	private _cmsRoles$ = new BehaviorSubject<CmsRole[] | null>(null);
  public cmsRoles$ = this._cmsRoles$.asObservable();
  private _myCmsRows$ = new BehaviorSubject<any[] | null>(null);
  private _myCmsReq$?: Observable<any[]>;

  constructor(private http: HttpClient) {    try {
      const cachedRows = JSON.parse(localStorage.getItem('cms_roles') || '[]');
      if (Array.isArray(cachedRows) && cachedRows.length) {
        this._myCmsRows$.next(cachedRows);
        const roles = cachedRows.filter((r: any) => r?.is_active).map((r: any) => r?.role as CmsRole);
        this._cmsRoles$.next(roles);
      }
    } catch {}}
  private isLoggedIn(): boolean {
    const token = localStorage.getItem('token');
    return !!token;
  }
  loadMyCmsRoles(options?: { silence?: boolean; force?: boolean }) {
    const silence = !!options?.silence;
    const force = !!options?.force;
    if (!this.isLoggedIn()) {
      this._myCmsRows$.next([]);
      if (!silence) this._cmsRoles$.next([]);
      return of([] as CmsRole[]);
    }

    // 1) Có cache & không force -> map ra roles rồi trả ngay
    const cached = this._myCmsRows$.value;
    if (cached && !force) {
      const roles = (cached || [])
        .filter((r: any) => r?.is_active)
        .map((r: any) => r?.role as CmsRole);
      if (!silence) this._cmsRoles$.next(roles);
      return of(roles);
    }

    // 2) Có request đang bay -> dùng lại đúng observable đó
    if (this._myCmsReq$ && !force) {
      return this._myCmsReq$.pipe(
        map(rows =>
          (rows || []).filter((r: any) => r?.is_active).map((r: any) => r?.role as CmsRole)
        ),
        tap(roles => { if (!silence) this._cmsRoles$.next(roles); }),
        shareReplay(1)
      );
    }

    // 3) Tạo request mới, share cho mọi subscriber
    const url = `${this.url}/cms/cms_role/my/`;
    this._myCmsReq$ = this.http.get<any[]>(url).pipe(
      tap((rows) => {
        this._myCmsRows$.next(rows || []);
        const roles = (rows || [])
          .filter((r: any) => r?.is_active)
          .map((r: any) => r?.role as CmsRole);
        if (!silence) this._cmsRoles$.next(roles);
        try {
          localStorage.setItem('cms_roles', JSON.stringify(rows || []));
          window.dispatchEvent(new CustomEvent('cms_roles_updated'));
        } catch {}
      }),
      finalize(() => { this._myCmsReq$ = undefined; }),
      shareReplay(1)
    );

    return this._myCmsReq$.pipe(
      map(rows =>
        (rows || []).filter((r: any) => r?.is_active).map((r: any) => r?.role as CmsRole)
      )
    );
  }

  hasAnyCms(roles: CmsRole[]): boolean {
    const current = this._cmsRoles$.value ?? [];
    return roles.some(r => current.includes(r));
  }

  uploadCmsAsset(file: File) {  
    const fd = new FormData();
    fd.append('file', file);
    return this.http.post<any>(`${this.url}/cms/upload/`, fd);
  }

  ensureCmsRolesLoaded() {
    if (this._cmsRoles$.value !== null) return this.cmsRoles$;
    return this.loadMyCmsRoles();
  }

  // ===== Helpers =====
  private asFormData(data: any): FormData {
    if (data instanceof FormData) return data;
    const fd = new FormData();
    Object.keys(data || {}).forEach((k) => {
      const v = (data as any)[k];
      if (v === undefined || v === null || v === '') return;
      if (Array.isArray(v)) fd.append(k, v.join(','));
      else fd.append(k, v);
    });
    return fd;
  }

  // Endpoint cho cms_content
  listContent(params: any = {}): Observable<any> {
    const payload = { ...params, include_scheduled: 1 };
    return this.http.get<any>(`${this.url}/cms/cms_content/`, {
      params: payload,
    });
  }

  getContentDetail(id: number | string): Observable<any> {
    return this.http.get<any>(`${this.url}/cms/cms_content/${id}/`);
  }

  createContent(data: any): Observable<any> {
    // Cho phép nhận FormData hoặc JSON
    const body = this.asFormData(data);
    return this.http.post<any>(`${this.url}/cms/cms_content/`, body);
  }

  updateContent(id: number | string, data: any): Observable<any> {
    // Đổi trạng thái => PATCH; cập nhật full (FormData) => PUT
    if (data instanceof FormData) {
      return this.http.put<any>(`${this.url}/cms/cms_content/${id}/`, data);
    }
    return this.http.patch<any>(`${this.url}/cms/cms_content/${id}/`, data);
  }

  bulkContentUpdateRoles(data: any): Observable<any> {
    return this.http.post<any>(`${this.url}/cms/cms_content/bulk_update_roles/`, data);
  }

  updateActiveContent(id: number | string, data: any): Observable<any> {
    return this.http.post<any>(`${this.url}/cms/cms_content/${id}/update_active/`, data);
  }

  deleteContent(id: number | string): Observable<any> {
    return this.http.delete<any>(`${this.url}/cms/cms_content/${id}/`);
  }

  // Endpoint for cms_qa viewset
  listQa(params: any = {}): Observable<any> {
    return this.http.get<any>(`${this.url}/cms/cms_qa/`, {
      params: params,
    });
  }

  getQaDetail(id: number | string): Observable<any> {
    return this.http.get<any>(`${this.url}/cms/cms_qa/${id}/`);
  }

  createQa(data: any): Observable<any> {
    const body = this.asFormData(data);
    return this.http.post<any>(`${this.url}/cms/cms_qa/`, body);
  }

  updateQa(id: number | string, data: any): Observable<any> {
    if (data instanceof FormData) {
      return this.http.put<any>(`${this.url}/cms/cms_qa/${id}/`, data);
    }
    return this.http.patch<any>(`${this.url}/cms/cms_qa/${id}/`, data);
  }

  updateActiveQa(id: number | string, data: any): Observable<any> {
    return this.http.post<any>(`${this.url}/cms/cms_qa/${id}/update_active/`, data);
  }

  bulkQaUpdateRoles(data: any): Observable<any> {
    return this.http.post<any>(`${this.url}/cms/cms_qa/bulk_update_roles/`, data);
  }

  deleteQa(id: number | string): Observable<any> {
    return this.http.delete<any>(`${this.url}/cms/cms_qa/${id}/`);
  }

  // Endpoint for cms_user_content viewset
  listUserContent(params: any = {}): Observable<any> {
    return this.http.get<any>(`${this.url}/cms/cms_user_content/`, {
      params: params,
    });
  }

  getUserContentDetail(id: number | string): Observable<any> {
    return this.http.get<any>(`${this.url}/cms/cms_user_content/${id}/`);
  }

  createUserContent(data: any): Observable<any> {
    return this.http.post<any>(`${this.url}/cms/cms_user_content/`, data);
  }

  updateUserContent(id: number | string, data: any): Observable<any> {
    return this.http.patch<any>(`${this.url}/cms/cms_user_content/${id}/`, data);
  }

  deleteUserContent(id: number | string): Observable<any> {
    return this.http.delete<any>(`${this.url}/cms/cms_user_content/${id}/`);
  }

  // Endpoint for cms_role viewset
  listRole(params: any = {}): Observable<any> {
    return this.http.get<any>(`${this.url}/cms/cms_role/`, {
      params: params,
    });
  }

  getRoleDetail(id: number | string): Observable<any> {
    return this.http.get<any>(`${this.url}/cms/cms_role/${id}/`);
  }

  createRole(data: any): Observable<any> {
    return this.http.post<any>(`${this.url}/cms/cms_role/`, data);
  }

  addMultiple(data: any): Observable<any> {
    return this.http.post<any>(`${this.url}/cms/cms_role/add-multiple/`, data);
  }

  updateRole(id: number | string, data: any): Observable<any> {
    return this.http.patch<any>(`${this.url}/cms/cms_role/${id}/`, data);
  }

  deleteRole(id: number | string): Observable<any> {
    return this.http.delete<any>(`${this.url}/cms/cms_role/${id}/`);
  }

  resetCmsRoles(): void {
    this._cmsRoles$.next(null);
  }

  forceReloadCmsRoles() {
    return this.loadMyCmsRoles();
  }

  // getMyCmsRoles(): Observable<any[]> {
  //   return this.http.get<any[]>(`${this.url}/cms/cms_role/my/`);
  // }
  getMyCmsRoles(): Observable<CmsRole[]> {
    return this.loadMyCmsRoles({ silence: true });
  }
}
