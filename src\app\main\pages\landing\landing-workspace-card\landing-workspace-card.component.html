<div class="workspace-feature-cards">
  <div class="workspace-feature-cards__container">
    <div
      *ngFor="let feature of features; let i = index"
      class="workspace-feature-cards__card"
    >
      <!-- <PERSON><PERSON> số thứ tự -->
      <div class="workspace-feature-cards__card-step">
        {{ i + 1 }}
      </div>

      <div class="workspace-feature-cards__card-content">
        <h4 class="workspace-feature-cards__card-title">
          {{ feature.title }}
        </h4>
        <p class="workspace-feature-cards__card-description">
          {{ feature.description }}
        </p>
      </div>

      <div class="workspace-feature-cards__card-image">
        <img
          [src]="feature.imageSrc"
          [alt]="feature.imageAlt"
          class="workspace-feature-cards__card-img"
        />
      </div>
    </div>
  </div>
</div>
