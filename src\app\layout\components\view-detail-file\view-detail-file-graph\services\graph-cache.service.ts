import { Injectable } from '@angular/core';
import { GraphApiResponse, ApiRelationship } from '../types/graph.types';
import { safeArray } from '../helper/helper';
import { RELATIONSHIP_DIRECTIONS } from '../constants/graph.constants';

/**
 * Service for managing graph data caches
 * Handles neighbor maps, adjacency maps, and protected node IDs caching
 */
@Injectable({
  providedIn: 'root'
})
export class GraphCacheService {
  private cachedNeighborMap: Map<string, Set<string>> | null = null;
  private cachedAdjacencyMap: Map<string, Set<string>> | null = null;
  private cachedProtectedNodeIds: Set<string> | null = null;

  /**
   * Build undirected neighbor map (bidirectional relationships)
   */
  buildUndirectedNeighborMap(graphData: GraphApiResponse | null): Map<string, Set<string>> {
    if (this.cachedNeighborMap) {
      return this.cachedNeighborMap;
    }

    const neighbors = new Map<string, Set<string>>();
    if (!graphData) {
      return neighbors;
    }

    const relationships = safeArray(graphData.relationships);
    for (const rel of relationships) {
      const a = String(rel.source_id);
      const b = String(rel.target_id);

      if (!neighbors.has(a)) neighbors.set(a, new Set());
      if (!neighbors.has(b)) neighbors.set(b, new Set());

      neighbors.get(a)!.add(b);
      neighbors.get(b)!.add(a);
    }

    this.cachedNeighborMap = neighbors;
    return neighbors;
  }

  /**
   * Build directed adjacency map (respects relationship direction)
   */
  buildAdjacencyMap(graphData: GraphApiResponse | null): Map<string, Set<string>> {
    if (this.cachedAdjacencyMap) {
      return this.cachedAdjacencyMap;
    }

    const adjacency = new Map<string, Set<string>>();
    if (!graphData) {
      return adjacency;
    }

    const relationships = safeArray(graphData.relationships);
    for (const rel of relationships) {
      const rawSourceId = String(rel.source_id);
      const rawTargetId = String(rel.target_id);
      const directionIsOutgoing = rel.huong === RELATIONSHIP_DIRECTIONS.OUTGOING;
      const sourceId = directionIsOutgoing ? rawSourceId : rawTargetId;
      const targetId = directionIsOutgoing ? rawTargetId : rawSourceId;

      if (!adjacency.has(sourceId)) {
        adjacency.set(sourceId, new Set());
      }
      adjacency.get(sourceId)!.add(targetId);
    }

    this.cachedAdjacencyMap = adjacency;
    return adjacency;
  }

  /**
   * Get protected node IDs (root node and seed nodes that should never be removed)
   */
  getProtectedNodeIds(
    graphData: GraphApiResponse | null,
    rootNodeId: string
  ): Set<string> {
    if (this.cachedProtectedNodeIds) {
      return this.cachedProtectedNodeIds;
    }

    const protectedIds = new Set<string>();
    if (rootNodeId) {
      protectedIds.add(rootNodeId);
    }
    const seedIds = graphData?.seed_node_ids;
    if (Array.isArray(seedIds)) {
      seedIds.forEach((id: string | number) => {
        if (id != null) {
          protectedIds.add(String(id));
        }
      });
    }

    this.cachedProtectedNodeIds = protectedIds;
    return protectedIds;
  }

  /**
   * Invalidate all caches
   */
  invalidateCaches(): void {
    this.cachedNeighborMap = null;
    this.cachedAdjacencyMap = null;
    this.cachedProtectedNodeIds = null;
  }

  /**
   * Clear specific cache
   */
  clearNeighborMap(): void {
    this.cachedNeighborMap = null;
  }

  clearAdjacencyMap(): void {
    this.cachedAdjacencyMap = null;
  }

  clearProtectedNodeIds(): void {
    this.cachedProtectedNodeIds = null;
  }
}

