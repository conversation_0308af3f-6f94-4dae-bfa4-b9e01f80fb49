// import {
//   HttpErrorResponse,
//   HttpEvent,
//   HttpHand<PERSON>,
//   HttpInterceptor,
//   HttpRequest,
//   HttpResponse,
// } from "@angular/common/http";
// import { Injectable } from "@angular/core";
// import { Observable } from "rxjs";
// import { tap } from "rxjs/operators";
// import { LoadingOverlayRef, LoadingService } from "./loading.service";

// export const InterceptorSkipHeader = "X-Skip-Interceptor";

// @Injectable({
//   providedIn: "root",
// })
// export class LoadingInterceptor implements HttpInterceptor {
//   private loadingRef: LoadingOverlayRef | null = null;

//   constructor(private loadingService: LoadingService) {}

//   intercept(
//     req: HttpRequest<any>,
//     next: HttpHandler
//   ): Observable<HttpEvent<any>> {
//     if (req.headers.has(InterceptorSkipHeader)) {
//       const headers = req.headers.delete(InterceptorSkipHeader);
//       req = req.clone({ headers });
//     } else {
//       if (!this.loadingRef) {
//         Promise.resolve().then(() => {
//           if (!this.loadingRef) {
//             this.loadingRef = this.loadingService.open();
//           }
//         });
//       }
//     }

//     return next.handle(req).pipe(
//       tap(
//         (event) => {
//           if (event instanceof HttpResponse && this.loadingRef) {
//             this.loadingRef.close();
//             this.loadingRef = null;
//           }
//         },
//         (err) => {
//           if (err instanceof HttpErrorResponse && this.loadingRef) {
//             this.loadingRef.close();
//             this.loadingRef = null;
//           }
//         }
//       )
//     );
//   }
// }
import {
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { finalize } from "rxjs/operators";
import { LoadingOverlayRef, LoadingService } from "./loading.service";

export const InterceptorSkipHeader = "X-Skip-Interceptor";

@Injectable({
  providedIn: "root",
})
export class LoadingInterceptor implements HttpInterceptor {
  // private loadingRef: LoadingOverlayRef | null = null;

  constructor(private loadingService: LoadingService) {}

  // intercept(
  //   req: HttpRequest<any>,
  //   next: HttpHandler
  // ): Observable<HttpEvent<any>> {
  //   if (req.headers.has(InterceptorSkipHeader)) {
  //     const headers = req.headers.delete(InterceptorSkipHeader);
  //     req = req.clone({ headers });
  //   } else {
  //     if (!this.loadingRef) {
  //       // Gọi mở loading ngay lập tức
  //       this.loadingRef = this.loadingService.open();
  //     }
  //   }

  //   return next.handle(req).pipe(
  //     finalize(() => {
  //       if (this.loadingRef) {
  //         this.loadingRef.close();
  //         this.loadingRef = null;
  //       }
  //     })
  //   );
  // }
  private loadingRef: any = null;
  private activeRequests = 0;

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    let shouldCount = !req.headers.has(InterceptorSkipHeader);

    // Nếu có header skip thì loại bỏ header đó và không tính vào loading
    if (!shouldCount) {
      const headers = req.headers.delete(InterceptorSkipHeader);
      req = req.clone({ headers });
    } else {
      // Nếu là request đầu tiên, mở loading
      if (this.activeRequests === 0) {
        this.loadingRef = this.loadingService.open();
      }
      this.activeRequests++;
    }

    return next.handle(req).pipe(
      finalize(() => {
        if (shouldCount) {
          this.activeRequests--;
          // Khi request cuối cùng hoàn thành, đóng loading
          if (this.activeRequests === 0 && this.loadingRef) {
            this.loadingRef.close();
            this.loadingRef = null;
          }
        }
      })
    );
  }
}
