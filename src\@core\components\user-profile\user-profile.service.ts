import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "environments/environment";
import { InterceptorSkipHeader } from "../loading/loading.interceptor";

@Injectable({
  providedIn: "root",
})
export class UserProfileService {
  constructor(private http: HttpClient) {}
  updateProfile(body) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.put(`${environment.apiUrl}/auth/update-profile`, body, { headers });
  }
}
