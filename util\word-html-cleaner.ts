import sanitizeHtml from "sanitize-html";

export function cleanWordHtml(html: string): string {
  if (!html) return "";

  const clean = sanitizeHtml(html, {
    allowedTags: sanitizeHtml.defaults.allowedTags.concat([
      "table",
      "thead",
      "tbody",
      "tfoot",
      "tr",
      "td",
      "th",
      "colgroup",
      "col",
      "span",
      "font"
    ]),

    // <PERSON><PERSON><PERSON> thuộ<PERSON> t<PERSON>h đ<PERSON> giữ lại
    allowedAttributes: {
      "*": ["style"],
      table: ["border", "cellpadding", "cellspacing", "width"],
      td: ["colspan", "rowspan", "style", "width", "height"],
      th: ["colspan", "rowspan", "style", "width", "height"],
      span: ["style"],
      font: ["color"]
    },

    // Style hợp lệ
    // allowedStyles: {
    //   "*": {
    //     "color": [/^.*$/],
    //     "background-color": [/^.*$/],
    //     "border": [/^.*$/],
    //     "border-collapse": [/^.*$/],
    //     "border-spacing": [/^.*$/],
    //     "text-align": [/^.*$/],
    //     "vertical-align": [/^.*$/],
    //     "width": [/^.*$/],
    //     "height": [/^.*$/],
    //     "font-weight": [/^.*$/],
    //     "font-style": [/^.*$/],
    //     "text-decoration": [/^.*$/],
    //     "padding": [/^.*$/],
    //     "margin": [/^.*$/],
    //   }
    // },
    allowedStyles: {
      "*": {
        "border": [/^[\s\S]+$/],
        "border-top": [/^[\s\S]+$/],
        "border-right": [/^[\s\S]+$/],
        "border-bottom": [/^[\s\S]+$/],
        "border-left": [/^[\s\S]+$/],
        "border-collapse": [/^[\s\S]+$/],
        "padding": [/^[\s\S]+$/],
        "padding-top": [/^[\s\S]+$/],
        "padding-right": [/^[\s\S]+$/],
        "padding-bottom": [/^[\s\S]+$/],
        "padding-left": [/^[\s\S]+$/],
        "margin": [/^[\s\S]+$/],
        "margin-top": [/^[\s\S]+$/],
        "margin-right": [/^[\s\S]+$/],
        "margin-bottom": [/^[\s\S]+$/],
        "margin-left": [/^[\s\S]+$/],
        "text-align": [/^[\s\S]+$/],
        "vertical-align": [/^[\s\S]+$/],
        "font-weight": [/^[\s\S]+$/],
        "width": [/^[\s\S]+$/],
        "height": [/^[\s\S]+$/],
        "background": [/^[\s\S]+$/],
        "background-color": [/^[\s\S]+$/],
      }
    },

    // Xoá thuộc tính rác
    disallowedTagsMode: "discard",

    // Bộ lọc nội dung (lọc rác của Word)
    transformTags: {
      "o:p": () => ({ tagName: "span", text: "" }),
      "xml": () => ({ tagName: "span", text: "" })
    }
  });

  // Clean lại <br> lặp
  return clean.replace(/(<br\s*\/?>\s*){2,}/g, "<br>").trim();
}
