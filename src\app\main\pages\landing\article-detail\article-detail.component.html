<app-landing-header
  [activeSection]="null"
  [showNav]="true"
  [logoSrc]="'assets/images/logo/COpenAIlogo.svg'"
  brandText="CLS"
  (sectionSelect)="goLandingSection($event)">
</app-landing-header>
<div class="article-detail" *ngIf="!notFound; else notFoundTpl">
  <nav class="breadcrumb" aria-label="Breadcrumb" *ngIf="!notFound">
    <span class="br-prefix">
      <button type="button" class="br-back" (click)="backToLanding()" aria-label="Quay lại">←</button>

      <a class="br-link"
        [routerLink]="['/pages/landing']"
        [queryParams]="{ focus: articleId }">
        Trang chủ
      </a>
      <span class="br-sep">/</span>
    </span>

    <span class="br-current" [title]="topic || title">
      {{ topic || title }}
    </span>
  </nav>

  <h1 class="article-detail__title">{{ title }}</h1>

  <div class="article-detail__meta" *ngIf="date">
    <span class="article-detail__date">{{ date }}</span>
  </div>
<!-- 
  <figure class="article-detail__cover" *ngIf="coverImage">
    <img [src]="coverImage" [alt]="title" />
  </figure> -->

  <p class="article-detail__desc" *ngIf="description">{{ description }}</p>

  <div class="article-detail__content" [innerHTML]="htmlContent"></div>
</div>

<ng-template #notFoundTpl>
  <div class="article-detail__notfound">
    Bài viết không tồn tại hoặc đã bị gỡ.
  </div>
</ng-template>
<app-landing-footer [url1]="urls?.url1"></app-landing-footer>