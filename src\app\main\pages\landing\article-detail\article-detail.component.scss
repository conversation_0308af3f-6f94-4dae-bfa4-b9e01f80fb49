.article-detail { max-width: 860px; margin: 0 auto; padding: 24px 16px; }
.article-detail__title { font-size: 28px; font-weight: 700; line-height: 1.25; }
.article-detail__meta { color: #6b7280; margin: 8px 0 16px; }
.article-detail__cover img { width: 100%; border-radius: 12px; }
.article-detail__content { margin-top: 24px; }
.article-detail__content img { max-width: 100%; height: auto; border-radius: 8px; }

.video-embed { width: 100%; aspect-ratio: 16/9; margin: 16px 0; }
.video-embed iframe { width: 100%; height: 100%; border: 0; border-radius: 12px; }
.video-file { width: 100%; border-radius: 12px; margin: 16px 0; }

.article-detail__content {
  font-size: 16px;
  line-height: 1.6;
  color: #222;

  p { margin: 0 0 1rem; }
  img { max-width: 100%; height: auto; display: block; margin: 1rem 0; }
  iframe, video { max-width: 100%; }
}

/* =========================
   BREADCRUMB — 2 cột Grid
   ========================= */
.breadcrumb{
  display: grid;
  grid-template-columns: max-content 1fr; /* cột 1: prefix, cột 2: tiêu đề */
  align-items: center;
  column-gap: 8px;
  row-gap: 4px;
  margin: 16px 0 8px;
  font-size: 14px;
  color: #6b7280;
}

/* Prefix “← Trang chủ /” chạy trong cột 1, giữ trên cùng khi tiêu đề dài */
.br-prefix{
  grid-column: 1;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;  /* luôn 1 dòng */
}

.br-link{
  color:#008fe3;
  text-decoration:none;
}
.br-link:hover{ text-decoration:underline; }
.br-sep{ opacity:.6; }

/* Tiêu đề ở cột 2, cho phép xuống dòng nhiều hàng, thẳng hàng sau dấu “/” */
.br-current{
  grid-column: 2;
  color:#111827;
  font-weight:500;
  white-space: normal;
  overflow-wrap: anywhere;  /* chống tràn ở màn hẹp */
  word-break: break-word;
  line-height: 1.4;
}

/* Nút back trong prefix */
.br-back{
  border:none;
  background:transparent;
  font-size:18px;
  line-height:1;
  cursor:pointer;
  padding:2px 6px;
  color:#2563eb;
}

/* ===== Header giữ nguyên ===== */
:root { --header-h: 80px; }

/* chừa chỗ cho header fixed để nội dung không bị đè */
.article-detail,
.article-detail__notfound,
.breadcrumb { padding-top: calc(var(--header-h) + 12px); }

/* Copy style tối thiểu từ landing header (giữ đúng class) */
.landing__header{
  display:flex; align-items:center; justify-content:space-between;
  height: var(--header-h);
  padding: 16px 32px;
  position: sticky; top: 0; z-index: 1000;
  background: transparent; transition: background-color .2s, box-shadow .2s;

  &.is-scrolled{
    position: fixed; left: 0; right: 0;
    background: #fff; box-shadow: 0 2px 8px rgba(0,0,0,.06);
  }

  &-logo{ display:flex; align-items:center; gap:16px; }
  &-logo-separator{ height:32px; border-left:1px solid #e0e0e0; }
  &-logo-text{ font-size:24px; font-weight:700; color:#008fe3; }

  &-action .landing__header-login-btn{
    background-color: color-mix(in srgb, #008fe3 10%, white);
    color:#008fe3; font-weight:600; padding:16px 32px;
    border:none; border-radius:41px; cursor:pointer; transition:all .3s;
  }
  &-action .landing__header-login-btn:hover{ background:#008fe3; color:#fff; }
}

/* ===== Nội dung chi tiết giữ nguyên ===== */
.article-detail__content,
.article-detail { overflow-x: hidden; }

:host ::ng-deep .article-detail__content img {
  display: block;
  width: 100% !important;  
  max-width: 100% !important;
  height: auto !important; 
  margin: 12px auto;
  border-radius: 8px;
  box-sizing: border-box;
}

:host ::ng-deep .article-detail__content video,
:host ::ng-deep .article-detail__content iframe {
  display: block;
  width: 100% !important;
  max-width: 100% !important;
  height: auto !important;
  margin: 12px auto;
}

.article-detail__content video,
.article-detail__content iframe {
  max-width: 100% !important;
  width: 100% !important;
}

.article-detail__content img,
.article-detail__content video,
.article-detail__content iframe {
  height: auto !important;
  display: block;
  margin: 12px auto;
  border-radius: 8px;
  box-sizing: border-box; 
}

.article-detail__content .ad-embed {
  position: relative;
  width: 100% !important;
  aspect-ratio: 16/9;
  margin: 16px 0;
}
.article-detail__content .ad-embed > iframe,
.article-detail__content .ad-embed > video {
  position: absolute;
  inset: 0;
  width: 100% !important;
  height: 100% !important;
  border: 0;
  border-radius: 12px;
}

.article-detail__content .ad-img {
  max-height: 70vh;
  height: auto !important;
  object-fit: contain !important;  
}

.article-detail__content .ad-tablewrap {
  width: 100%;
  overflow-x: auto;
  margin: 12px 0;
}

.article-detail__desc {
  color: #6b7280;
  font-style: italic;
  margin: 12px 0 16px;
  line-height: 1.6;
}

:host ::ng-deep .app-content.content,
:host ::ng-deep .app-content.content.px-0 {
  padding: 0 !important;
  background-color: transparent !important;
}
:host ::ng-deep .content-wrapper {
  padding: 0 !important;
  background-color: transparent !important;
}
:host ::ng-deep .article-detail__content video,
:host ::ng-deep .article-detail__content iframe {
  display: block;
  width: 100% !important;
  max-width: 100% !important;
  aspect-ratio: 16 / 9;
  height: auto !important;
  margin: 12px auto;
  border-radius: 8px;
  box-sizing: border-box;
}