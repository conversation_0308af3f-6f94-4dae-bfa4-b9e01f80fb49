FROM node:14.21.3-bullseye as build

WORKDIR /app
RUN npm i -g @angular/cli@14.0.6

COPY ["package.json", "./"]

RUN npm install

COPY . .

RUN ng build --configuration devui --output-path=dist/cls

FROM nginx:latest as deploy

COPY /nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build /app/dist/cls/ /app/
# RUN rm /app//index.html
# RUN cp /app//cls/index.html /app//index.html
EXPOSE 80
WORKDIR /app
COPY replace_tag.sh .
# CMD bash /app/replace_tag.sh && nginx -g 'daemon off;'
CMD nginx -g 'daemon off;'