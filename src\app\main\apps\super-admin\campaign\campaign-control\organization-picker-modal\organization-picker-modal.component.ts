import { Component, Input } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

// Chỉ sử dụng tổ chức lớn và tổ chức con
type NodeType = "org" | "group";

export interface TreeNode {
  id: string | number;
  name: string;
  type: NodeType;
  email?: string;
  children?: TreeNode[];
  expanded?: boolean;
  checked?: boolean;
  partial?: boolean;
  parent?: TreeNode;
}

@Component({
  selector: 'app-organization-picker-modal',
  templateUrl: './organization-picker-modal.component.html',
  styleUrls: ['./organization-picker-modal.component.scss']
})
export class OrganizationPickerModalComponent {
  @Input() data: TreeNode[] = [];
  @Input() preselectedIds: Array<string | number> = [];

  public searchText = "";

  private preselectedSet = new Set<string>();

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit() {
    // Lọ<PERSON> dữ liệu để chỉ còn lại node type là "org" hoặc "group"

    // console.log(this.data)
    // console.log(this.preselectedIds)
    this.data = this.filterOrganizationNodes(this.data);

    this.attachParent(this.data, undefined);

    this.preselectedSet = new Set(
      (this.preselectedIds || []).map((v) => String(v).trim())
    );

    if (this.preselectedSet.size > 0) {
      this.applyPreselected(this.data);
    }
  }

  // Đệ quy để loại bỏ tất cả node type "user"
  private filterOrganizationNodes(nodes: any[]): TreeNode[] {
    if (!nodes) return [];
    return nodes
      .filter((n) => n.type === "org" || n.type === "group")
      .map((n) => ({
        ...n,
        children: this.filterOrganizationNodes(n.children ?? []),
      }));
  }

  private attachParent(nodes: TreeNode[], parent?: TreeNode) {
    for (const n of nodes || []) {
      n.parent = parent;
      n.expanded = n.expanded ?? true;
      if (n.children?.length) this.attachParent(n.children, n);
    }
  }

  private applyPreselected(nodes: TreeNode[]) {
    for (const n of nodes || []) {
      const nodeKey = String(n.id).trim();
      if (this.preselectedSet.has(nodeKey)) {
        n.checked = true;
        n.partial = false;
      }
      if (n.children?.length) this.applyPreselected(n.children);
    }
  }

  toggleExpand(n: TreeNode) {
    n.expanded = !n.expanded;
  }

  onCheck(n: TreeNode, checked: boolean) {
    n.checked = checked;
    n.partial = false;
  }

  matches(n: TreeNode, q: string): boolean {
    if (!q) return true;
    const t = q.toLowerCase();
    if (n.name.toLowerCase().includes(t) || n.email?.toLowerCase().includes(t))
      return true;
    return (n.children ?? []).some((c) => this.matches(c, q));
  }

  get selectedCount(): number {
    let count = 0;
    const walk = (ns: TreeNode[]) => {
      for (const n of ns || []) {
        if ((n.type === "org" || n.type === "group") && n.checked) count++;
        if (n.children?.length) walk(n.children);
      }
    };
    walk(this.data);
    return count;
  }

  confirm() {
    const picked: TreeNode[] = [];
    const walk = (ns: TreeNode[]) => {
      for (const n of ns || []) {
        if (n.checked) picked.push(n);
        if (n.children?.length) walk(n.children);
      }
    };
    walk(this.data);
    this.activeModal.close(picked);
  }

  cancel() {
    this.activeModal.dismiss();
  }
}
