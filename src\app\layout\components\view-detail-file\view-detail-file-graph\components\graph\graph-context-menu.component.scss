.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 180px;
  padding: 4px 0;

  ul {
    list-style: none;
    margin: 0;
    padding: 0;

    li {
      padding: 8px 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f5f5f5;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          background-color: transparent;
        }
      }

      span {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      &.has-submenu {
        position: relative;

        .menu-item-wrapper {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0;

          .menu-item-content {
            display: flex;
            align-items: center;
            flex: 1;
          }

          .submenu-trigger {
            width: 16px;
            height: 16px;
            margin: 0;
            padding: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .submenu {
          position: absolute;
          left: 100%;
          top: 0;
          background: white;
          border: 1px solid #ddd;
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          min-width: 180px;
          padding: 4px 0;
          margin-left: 4px;

          li {
            padding: 8px 16px;
          }
        }
      }
    }
  }
}

