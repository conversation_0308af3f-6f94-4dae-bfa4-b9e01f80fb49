import { Injectable } from "@angular/core";
import { environment } from "environments/environment";
import { Subject } from "rxjs";
import { w3cwebsocket as WebSocketClient } from "websocket";
import { LegalProgress } from "../models/status";

@Injectable({
  providedIn: "root",
})
export class WebSocketService {
  private client!: WebSocketClient;
  public messageSubject = new Subject<any>(); // Subject để phát dữ liệu nhận đư<PERSON> từ server
  private shouldReconnect: boolean = true;
  public countReconnect: number = 0; // Biến đếm số lần kết nối lại

  constructor() {}

  // 🔑 Check JWT có hết hạn chưa
  private isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split(".")[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }

  // 🔑 Lấy token hợp lệ (tự refresh nếu cần)
  private async getValidToken(): Promise<string | null> {
    let token = localStorage.getItem("token");
    if (!token || this.isTokenExpired(token)) {
      const refresh = localStorage.getItem("refresh_token");
      if (!refresh) return null;

      try {
        const res = await fetch(`${environment.apiUrl}/auth/refresh/`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ refresh }),
        });
        if (res.ok) {
          const data = await res.json();
          token = data.access;
          localStorage.setItem("token", token);
        } else {
          console.error("Refresh token failed");
          return null;
        }
      } catch (err) {
        console.error("Error refreshing token:", err);
        return null;
      }
    }
    return token;
  }

  // 🔑 Kết nối WebSocket
  async connect(url?: string): Promise<void> {
    const token = await this.getValidToken();
    if (!token) {
      console.error("Không có token hợp lệ để kết nối WebSocket");
      return;
    }

    const socketUrl =
      url || `${environment.apiSocket}/notifications?token=${token}`;

    if (this.client && this.client.readyState === this.client.OPEN) {
      // console.log("WebSocket đã được kết nối.");
      return;
    }

    this.client = new WebSocketClient(socketUrl);

    this.client.onerror = () => {
      // console.log("Connection Error");
    };

    this.client.onopen = () => {
      // console.log(`WebSocket client connected ${new Date().toLocaleString()}`);
    };

    this.client.onclose = async (e) => {
      // console.log("Closed:", e.code, e.reason);

      if (this.shouldReconnect) {
        this.countReconnect++;
        // 🔄 Thêm delay tránh spam reconnect
        setTimeout(async () => {
          await this.connect();
        }, 2000);
      }
    };

    this.client.onmessage = (e) => {
      if (e.data) {
        try {
          const data = JSON.parse(e.data as string);
          this.handleMessage(data);
        } catch (error) {
          console.error("Lỗi parse dữ liệu:", error);
        }
      }
    };
  }

  // Xử lý dữ liệu nhận được
  private handleMessage(data: any) {
    let event = "xử lý";
    switch (data.event) {
      case LegalProgress.CHUYEN_DOI_VB:
        event = "xử lý số hóa";
        break;
      case LegalProgress.DOI_CHIEU_VB:
        event = "đối chiếu";
        break;
      case LegalProgress.SO_SANH_VB:
        event = "bóc tách";
        break;
      case LegalProgress.THAM_QUYEN_NOI_DUNG:
        event = "phân tích nội dung văn bản";
        break;
      case LegalProgress.RA_SOAT_DIEU_KHOAN:
        event = "rà soát điều khoản";
        break;
      case LegalProgress.TRICH_XUAT_THONG_TIN:
        event = "trích xuất thông tin văn bản";
        break;
      case LegalProgress.LUU_TRU_NOI_DUNG:
        event = "lưu trữ nội dung vào CSDL";
        break;
      case LegalProgress.LEGAL_ANALYZE_DOING:
        event = "Đang tiến hành rà soát";
        break;
      case LegalProgress.SUMMARIZE_DOING:
        event = "Đang tóm tắt";
        break;
    }

    this.messageSubject.next({ event, data }); // Phát dữ liệu ra ngoài để component sử dụng
  }

  // Gửi tin nhắn qua WebSocket
  sendMessage(message: any) {
    if (this.client && this.client.readyState === this.client.OPEN) {
      this.client.send(JSON.stringify(message));
    } else {
      console.warn("WebSocket chưa được kết nối.");
    }
  }

  // Ngắt kết nối WebSocket
  disconnect() {
    this.shouldReconnect = false;
    if (this.client) {
      this.client.close();
      // console.log(`WebSocket disconnected at ${new Date().toLocaleString()}`);
    }
  }

  // 🔑 Reconnect thủ công (ví dụ khi login lại)
  async reconnect(): Promise<void> {
    this.disconnect();
    await this.connect();
  }
}
