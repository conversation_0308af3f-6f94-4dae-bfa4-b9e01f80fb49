import {
  Component,
  OnInit,
  ViewEncapsulation,
  OnDestroy,
  Input,
  OnChanges,
  Output,
  EventEmitter,
  SimpleChanges,
  ChangeDetectorRef,
  ViewChild,
} from '@angular/core';
import { ColumnMode } from '@swimlane/ngx-datatable';
import { formatRelativeTime } from '../../super-admin/email/helpers';
import Swal from 'sweetalert2';
import { ToastrService } from 'ngx-toastr';
import { CmsService } from '../cms.service';
import { AuthenticationService } from 'app/auth/service';

@Component({
  selector: 'app-list-post',
  templateUrl: './list-post.component.html',
  styleUrls: ['./list-post.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ListPostComponent implements OnInit, OnDestroy, OnChanges {
  @ViewChild('ContentTable') table: any;
  

  @Input() public content: any[] = [];
  @Input() public writerUsers: any[] = [];
  @Input() public reviewerUsers: any[] = [];
  @Input() public adminUsers: any[] = [];

  @Output() createdOrUpdated: EventEmitter<any> = new EventEmitter<any>();

  public activeTabId = 'ALL';

  public ColumnMode = ColumnMode;
  public totalItem: number = 0;
  public page: number = 0;

  public filteredContent: any[] = [];
  public isWriter = false;
  public isReviewer = false;
  public isAdmin = false;

  private getMyUserId(): number | null {
    const me = (this as any)._auth?.currentUserValue;
    return me?.id ?? null;
  }
  private getEntityId(entity: any): number | null {
    if (entity == null) return null;
    if (typeof entity === 'number') return entity;
    if (typeof entity === 'object') {
      return entity.id ?? entity.user?.id ?? entity.user_info?.id ?? entity.user_id ?? null;
    }
    return null;
  }

  private findUserById(id: any, pool: any[] = []): any | null {
    if (id == null) return null;
    return (pool || []).find(u =>
      u?.id === id || u?.user_id === id || u?.uid === id || u?.user?.id === id || u?.user_info?.id === id
    ) || null;
  }
  get allContentCount(): number {
    return this.applyWriterScope(this.content || []).length;
  }
  private normalizeUserRef(userRef: any, pool: any[] = []): any | null {
    if (!userRef) return null;

    if (typeof userRef === 'object') {
      const fullname =
        userRef.fullname || userRef.name || userRef.username ||
        userRef.user?.fullname || userRef.user_info?.fullname || null;

      if (fullname) return { ...userRef, fullname };

      const fallback =
        userRef.user?.name || userRef.user_info?.name ||
        userRef.user?.username || userRef.user_info?.username || null;

      return { ...userRef, fullname: fallback || '(không rõ tên)' };
    }

    const found = this.findUserById(userRef, pool);
    if (found) {
      const fullname = found.fullname || found.name || found.username ||
        found.user?.fullname || found.user_info?.fullname ||
        found.user?.name || found.user_info?.name ||
        found.user?.username || found.user_info?.username ||
        '(không rõ tên)';

      const id =
        found.id ?? found.user_id ?? found.uid ?? found.user?.id ?? found.user_info?.id ?? userRef;

      return { id, fullname, ...found };
    }

    return { id: userRef, fullname: '(không rõ tên)' };
  }

  private computeRoles() {
    const myId = this.getMyUserId();
    const hasMe = (arr: any[]) =>
      Array.isArray(arr) && arr.some(u => {
        const id = this.getEntityId(u?.user ?? u?.user_info ?? u);
        return id === myId;
      });

    this.isWriter   = hasMe(this.writerUsers || []);
    this.isReviewer = hasMe(this.reviewerUsers || []);
    this.isAdmin    = hasMe(this.adminUsers || []);
  }

  private applyWriterScope(list: any[]): any[] {
    if (this.isAdmin || this.isReviewer) return list;
    if (this.isWriter) {
      const myId = this.getMyUserId();
      if (!myId) return [];
      return list.filter(item => this.getEntityId(item?.writer) === myId);
    }
    return list;
  }

  private readonly RESTRICTED: string[] = ['DRAFT','WRITING','REVIEWING'];
  private applyWriterScopeForStatus(list: any[], status: string): any[] {
    if (this.isAdmin || this.isReviewer) return list;
    if (!this.isWriter) return list;

    const myId = this.getMyUserId();
    if (!myId) return [];

    if (status === 'ALL') {
      return list.filter(it =>
        this.RESTRICTED.includes(it?.status) ? (this.getEntityId(it?.writer) === myId) : true
      );
    }
    if (this.RESTRICTED.includes(status)) {
      return list.filter(it => this.getEntityId(it?.writer) === myId);
    }
    return list;
  }

  constructor(
    private _toastSerive: ToastrService,
    private _cmsService: CmsService,
    private cdr: ChangeDetectorRef,
    private _auth: AuthenticationService, 
  ) { }

  formatRelativeTime = formatRelativeTime;

  get draftContent() {
    const base = (this.content ?? []).filter(item => item.status === 'DRAFT');
    return this.applyWriterScope(base);
  }
  get writingContent() {
    const base = (this.content ?? []).filter(item => item.status === 'WRITING');
    return this.applyWriterScope(base);
  }
  get reviewingContent() {
    const base = (this.content ?? []).filter(item => item.status === 'REVIEWING');
    return this.applyWriterScope(base);
  }

  get completedContent() {
    const base = (this.content ?? []).filter(it => it.status === 'COMPLETED');
    return this.applyWriterScope(base);
  }
  get publishedContent() {
    const base = (this.content ?? []).filter(it => it.status === 'PUBLISHED');
    return this.applyWriterScope(base);
  }
  get unpublishedContent() {
    const base = (this.content ?? []).filter(it => it.status === 'UNPUBLISHED');
    return this.applyWriterScope(base);
  }
  get rejectedContent() {
    const base = (this.content ?? []).filter(it => it.status === 'REJECTED');
    return this.applyWriterScope(base);
  }
  get archivedContent() {
    const base = (this.content ?? []).filter(it => it.status === 'ARCHIVED');
    return this.applyWriterScope(base);
  }

  onActivate(event) {}

  onPage(event) {}

  onSort(event) {}

  onTabChange(status: string) {
    if (this.table && 'offset' in this.table) {
      this.table.offset = 0;
    }
    this.getFilteredContent(status);
  }
  formatYMD(d: string | Date) {
    const dt = new Date(d);
    if (isNaN(+dt)) return '';
    const dd = String(dt.getDate()).padStart(2, '0');
    const mm = String(dt.getMonth() + 1).padStart(2, '0');
    const yyyy = dt.getFullYear();
    return `${dd}/${mm}/${yyyy}`;   // dd/mm/yyyy
  }

  formatYMDHM(d: string | Date) {
    const dt = new Date(d);
    if (isNaN(+dt)) return '';
    const dd = String(dt.getDate()).padStart(2, '0');
    const mm = String(dt.getMonth() + 1).padStart(2, '0');
    const yyyy = dt.getFullYear();
    const hh = String(dt.getHours()).padStart(2, '0');
    const mi = String(dt.getMinutes()).padStart(2, '0');
    return `${dd}/${mm}/${yyyy} ${hh}:${mi}`;  // dd/mm/yyyy HH:mm
  }
  isFuture(d: string | Date) {
    const dt = new Date(d);
    return !isNaN(+dt) && dt.getTime() > Date.now();
  }
  formatDDMMYYYY(d: string | Date) {
    const dt = new Date(d);
    if (isNaN(+dt)) return '';
    const dd = String(dt.getDate()).padStart(2, '0');
    const mm = String(dt.getMonth() + 1).padStart(2, '0');
    const yyyy = dt.getFullYear();
    return `${dd}${mm}${yyyy}`;
  }

  formatDDMMYYYY_HHMM(d: string | Date) {
    const dt = new Date(d);
    if (isNaN(+dt)) return '';
    const dd = String(dt.getDate()).padStart(2, '0');
    const mm = String(dt.getMonth() + 1).padStart(2, '0');
    const yyyy = dt.getFullYear();
    const hh = String(dt.getHours()).padStart(2, '0');
    const mi = String(dt.getMinutes()).padStart(2, '0');
    return `${dd}${mm}${yyyy} ${hh}:${mi}`;
  }
  compareNumber = (a: any, b: any) => (Number(a) || 0) - (Number(b) || 0);

  getUserName(userRef: any, list: any[] = []): string {
    if (!userRef) return '—';

    if (typeof userRef === 'object') {
      return userRef.fullname || userRef.name || userRef.username || '—';
    }

    const found = (list || []).find(u =>
      u?.id === userRef || u?.user_id === userRef || u?.uid === userRef
    );
    return found?.fullname || found?.name || found?.username || '—';
  }

  getFilteredContent(status: string) {
    let list = Array.isArray(this.content) ? [...this.content] : [];
    if (status !== 'ALL') list = list.filter(item => item.status === status);

    // list = this.applyWriterScopeForStatus(list, status);
    list = this.applyWriterScope(list);

    this.filteredContent = list.map(it => ({
      ...it,
      writer: this.normalizeUserRef(it?.writer, this.writerUsers) || null,
      reviewer: this.normalizeUserRef(it?.reviewer, this.reviewerUsers) || null
    }));

    this.totalItem = this.filteredContent.length;
    this.page = 1;
    this.cdr.markForCheck();

  }

  updateActiveContent(id) {
    const data = null;
    this._cmsService.updateActiveContent(id, data).subscribe(
      (res) => {
        this.createdOrUpdated.emit(res);
        this._toastSerive.success("Đã cập nhật bài viết", "Thành công")
      },
      (err) => {
        this._toastSerive.error("Có lỗi xảy ra khi cập nhật bài viết", "Lỗi")
      }
    );
  }

  deleteContent(id) {
    Swal.fire({
      title: "Bạn chắc chắn muốn xóa?",
      text: "Hành động này không thể hoàn tác",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
      confirmButtonColor: "#d33",
    }).then((result) => {
      if (result.isConfirmed) {
        this._cmsService.deleteContent(id).subscribe(
          (res) => {
            this.createdOrUpdated.emit(res);
            this._toastSerive.success("Đã xoá bài viết", "Thành công")
          },
          (err) => {
            this._toastSerive.error("Có lỗi xảy ra khi xóa bài viết", "Lỗi")
          }
        );
      }
    });
  }

  ngOnInit(): void {
    this.computeRoles();
    this.getFilteredContent(this.activeTabId);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if ('writerUsers' in changes || 'reviewerUsers' in changes || 'adminUsers' in changes) {
      this.computeRoles();
    }
    if ('content' in changes) {
      this.getFilteredContent(this.activeTabId);
    }
  }

  ngOnDestroy(): void {}

}
