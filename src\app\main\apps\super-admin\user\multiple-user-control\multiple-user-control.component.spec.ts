import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MultipleUserControlComponent } from './multiple-user-control.component';

describe('MultipleUserControlComponent', () => {
  let component: MultipleUserControlComponent;
  let fixture: ComponentFixture<MultipleUserControlComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ MultipleUserControlComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MultipleUserControlComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
