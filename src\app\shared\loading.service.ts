// src/app/shared/loading.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class LoadingService {
  // <PERSON><PERSON>
  readonly navigating$ = new BehaviorSubject<boolean>(false);

  // HTTP pending
  private pendingCount = 0;
  readonly httpLoading$ = new BehaviorSubject<boolean>(false);

  readonly articleGate$ = new BehaviorSubject<boolean>(false);

  // incPending(): void {
  //   this.pendingCount++;
  //   if (this.pendingCount === 1) this.httpLoading$.next(true);
  // }
  decPending(): void {
    this.pendingCount = Math.max(0, this.pendingCount - 1);
    if (this.pendingCount === 0) this.httpLoading$.next(false);
  }
}
