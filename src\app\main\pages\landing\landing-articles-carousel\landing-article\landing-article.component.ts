import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { LoadingService } from 'app/shared/loading.service';
export interface Article {
  id: string;
  title: string;
  description?: string;
  date: string;
  imageSrc: string;
  imageAlt: string;
  link: string;
  slug?: string;
  status?: 'DRAFT' | 'PUBLISHED' | string;
  type?: 'NORMAL' | 'HOT' | string;
  coverImage?: string;
  publishedAt?: string;
  content?: string;
}

@Component({
  selector: 'app-landing-article',
  templateUrl: './landing-article.component.html',
  styleUrls: ['./landing-article.component.scss']
})
export class LandingArticleComponent {
  @Input() article: Article;
  
  constructor(private router: Router, private loading: LoadingService) {}

  openArticle(ev: MouseEvent) {
    ev.preventDefault();
    this.loading.articleGate$.next(true);
    this.loading.navigating$.next(true);
    // <PERSON>i<PERSON><PERSON> hướng
    const link = ['/pages', 'articles', this.article.slug || this.article.id];
    this.router.navigate(link);
  }
}
