// Constant for VPQH hostname
export const VPQH_HOSTNAME = "qlegal.quochoi.vn";
// export const VPQH_HOSTNAME = "cls-dev.cmcai.vn"
// export const VPQH_HOSTNAME = "localhost";

// Constant for VPQH theme color code
export const VPQH_COLOR_CODE = "#d83327";

// Helper function to check if current hostname is VPQH
export function isVPQHDomain(): boolean {
  return window.location.hostname.includes(VPQH_HOSTNAME);
}

export const DEFAULT_LOGO_IMAGE = isVPQHDomain()
  ? "assets/images/logo/VPQH.png"
  : "assets/images/logo/CMC.png";

export const DEFAULT_LOGIN_BACKGROUND = isVPQHDomain()
  ? "assets/images/illustration/VPQH-background.png"
  : "assets/images/illustration/loginBG.png";

export function getLogoImage(): string {
  const logoImage = localStorage.getItem("logo_image");

  if (!logoImage || logoImage === "null" || logoImage === "undefined") {
    return DEFAULT_LOGO_IMAGE;
  }

  return logoImage;
}

export function getLogoFavicon(): string {
  const logoImage = localStorage.getItem("logo_image");

  if (!logoImage || logoImage === "null" || logoImage === "undefined") {
    // Check if hostname includes VPQH hostname for VPQH version
    if (isVPQHDomain()) {
      return "assets/images/logo/VPQH.png";
    }
    return "assets/images/favicon.ico";
  }

  return logoImage;
}

export function getAppName(): string {
  const appName = localStorage.getItem("app_name");

  if (!appName || appName === "null" || appName === "undefined") {
    // Check if hostname includes VPQH hostname for VPQH version
    if (isVPQHDomain()) {
      return "Q-Legal";
    }
    return "CLS";
  }

  return appName;
}

/**
 * Sets the login background image based on hostname and localStorage
 * This function centralizes the background image logic for login and maintain pages
 */
export function setLoginBackground(): void {
  const stored = localStorage.getItem("background_image");
  const candidate =
    stored &&
    stored !== "null" &&
    stored !== "undefined" &&
    stored.trim() !== ""
      ? stored.trim()
      : null;
  const fallback = DEFAULT_LOGIN_BACKGROUND;
  const apply = (url: string) =>
    document.documentElement.style.setProperty("--login-bg", `url(${url})`);

  if (!candidate) {
    apply(fallback);
    return;
  }

  const img = new Image();
  img.onload = () => apply(candidate);
  img.onerror = () => {
    localStorage.removeItem("background_image");
    apply(fallback);
  };
  img.src = candidate;
}
