import { Component, OnInit } from '@angular/core';
import { CmsService } from './cms.service';
import { forkJoin } from 'rxjs';
import { AuthenticationService } from 'app/auth/service'; 

@Component({
  selector: 'app-cms',
  templateUrl: './cms.component.html',
  styleUrls: ['./cms.component.scss']
})
export class CmsComponent implements OnInit {

  public breadcrumb = {
    links: [
      {
        name: "Quản lý công việc của tôi",
        isHeader: true,
      },
    ],
  };

  public writerUsers: any[] = [];
  public reviewerUsers: any[] = [];
  public adminUsers: any[] = [];

  public cmsData: { content: any[]; qa: any[] } = { content: [], qa: [] };
  private isWriter = false;
  private isReviewer = false;
  private isAdmin = false;
  constructor(
    private cmsService: CmsService,
    private _auth: AuthenticationService,
  ) { }
  private getMyUserId(): number | null {
    const me = (this as any)._auth?.currentUserValue;
    return me?.id ?? null;
  }
  private getEntityId(entity: any): number | null {
    if (entity == null) return null;
    if (typeof entity === 'number') return entity;
    if (typeof entity === 'object') {
      return entity.id ?? entity.user?.id ?? entity.user_info?.id ?? entity.user_id ?? null;
    }
    return null;
  }
  private computeRoles() {
    const myId = this.getMyUserId();
    const hasMe = (arr: any[]) =>
      Array.isArray(arr) && arr.some(u => {
        const id = this.getEntityId(u?.user ?? u?.user_info ?? u);
        return id === myId;
      });

    this.isWriter   = hasMe(this.writerUsers || []);
    this.isReviewer = hasMe(this.reviewerUsers || []);
    this.isAdmin    = hasMe(this.adminUsers || []);
  }

  private readonly RESTRICTED = ['DRAFT','WRITING','REVIEWING'];
  private filterContentForVisibleCount(list: any[]): any[] {
    if (this.isAdmin || this.isReviewer) return list || [];
    if (!this.isWriter) return list || [];

    const myId = this.getMyUserId();
    if (!myId) return [];

    // tab "ALL": thấy tất cả, nhưng với 3 trạng thái hạn chế thì chỉ thấy bài mình
    return (list || []).filter(it =>
      this.RESTRICTED.includes(it?.status)
        ? (this.getEntityId(it?.writer) === myId)
        : true
    );
  }
  private applyWriterScope(list: any[]): any[] {
    if (this.isAdmin || this.isReviewer) return list || [];
    if (!this.isWriter) return list || [];
    const myId = this.getMyUserId();
    if (!myId) return [];
    return (list || []).filter(it => this.getEntityId(it?.writer) === myId);
  }
  private filterQaForVisibleCount(list: any[]): any[] {
    if (this.isAdmin || this.isReviewer) return list || [];
    if (!this.isWriter) return list || [];

    const myId = this.getMyUserId();
    if (!myId) return [];

    return (list || []).filter(it =>
      this.RESTRICTED.includes(it?.status)
        ? (this.getEntityId(it?.writer) === myId)
        : true
    );
  }

  get visibleContentCount(): number {
    return this.applyWriterScope(this.cmsData?.content || []).length;
  }
  get visibleQaCount(): number {
    return this.applyWriterScope(this.cmsData?.qa || []).length;
  }

  listCmsUser() {
    this.cmsService.listRole().subscribe(res => {
      this.writerUsers = [];
      this.reviewerUsers = [];
      this.adminUsers = [];

      for (const r of res) {
        const normalized = {
          ...r,
          // luôn đảm bảo tồn tại r.user { id, fullname, email }
          user: r.user ?? r.user_info ?? r,
        };

        if (normalized.role === 'WRITER') {
          this.writerUsers.push(normalized);
        }
        if (normalized.role === 'REVIEWER') {
          this.reviewerUsers.push(normalized);
        }
        if (normalized.role === 'ADMIN') {
          this.adminUsers.push(normalized);
        }
      }
      this.computeRoles(); 
    });
  }

  getCmsData() {
    forkJoin([
      this.cmsService.listContent(),
      this.cmsService.listQa(),
    ]).subscribe(
      ([contentRes, qaRes]) => {
        this.cmsData = {
          content: contentRes,
          qa: qaRes,
        };
      },
      (err) => {
        this.cmsData = { content: [], qa: [] };
      }
    );
  }

  // ngOnInit(): void {
  //   this.listCmsUser();
  //   this.getCmsData();
  // }
  async ngOnInit(): Promise<void> {
    await this.listCmsUser();
    this.getCmsData();
  }
}
