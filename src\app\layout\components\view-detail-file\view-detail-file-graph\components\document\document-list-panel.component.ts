import { Component, Input, Output, EventEmitter, ViewChild, AfterViewChecked, ChangeDetectorRef } from '@angular/core';
import { GraphFormState } from '../../types/graph.types';
import { ApiNode } from '../../types/graph.types';
import { FlatpickrOptions } from 'ng2-flatpickr';
import { Ng2FlatpickrComponent } from 'ng2-flatpickr';

export interface DocumentListItem {
  id: string;
  title: string;
  selected: boolean;
  apiNode: ApiNode;
}

@Component({
  selector: 'app-document-list-panel',
  templateUrl: './document-list-panel.component.html',
  styleUrls: ['./document-list-panel.component.scss'],
})
export class DocumentListPanelComponent implements AfterViewChecked {
  @ViewChild('yearPicker', { static: false }) yearPicker?: Ng2FlatpickrComponent;
  
  @Input() expanded: boolean = false;
  @Input() documentList: DocumentListItem[] = [];
  @Input() documentListSearch: string = '';
  @Input() selectAllDocuments: boolean = false;
  @Input() highlightedDocumentId: string | null = null;
  @Input() activeDocumentTab: string = 'timkiem';
  @Input() formState: GraphFormState | null = null;
  @Input() boLocMoiQuanHeOptions: any[] = [];
  @Input() coQuanBanHanhOptions: any[] = [];
  @Input() boLocLoaiVanBanOptions: any[] = [];
  @Input() tinhTrangHieuLucOptions: any[] = [];
  @Input() customYearOptions: FlatpickrOptions = {};
  
  private datePickerInitialized: boolean = false;
  
  constructor(private cdr: ChangeDetectorRef) {}

  @Output() toggle = new EventEmitter<void>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() selectAllChange = new EventEmitter<boolean>();
  @Output() documentSelectChange = new EventEmitter<{ item: DocumentListItem; selected: boolean }>();
  @Output() toggleFullTable = new EventEmitter<void>();
  @Output() viewModeChange = new EventEmitter<string>();
  @Output() moiQuanHeSelectionChange = new EventEmitter<string[]>();
  @Output() coQuanBanHanhSelectionChange = new EventEmitter<string[]>();
  @Output() loaiVanBanSelectionChange = new EventEmitter<string[]>();
  @Output() trangThaiHieuLucSelectionChange = new EventEmitter<string[]>();
  @Output() yearChange = new EventEmitter<any>();
  @Output() dateFilterModeChange = new EventEmitter<{ mode: 'ban_hanh' | 'hieu_luc' | null; event: Event }>();
  @Output() activeTabChange = new EventEmitter<string>();
  @Output() documentHover = new EventEmitter<string | null>();

  getFilteredDocumentList(): DocumentListItem[] {
    const query = (this.documentListSearch || '').toLowerCase().trim();
    if (!query) {
      return this.documentList;
    }
    return this.documentList.filter((item) => {
      const normalizedTitle = (item.title || '').toLowerCase();
      return normalizedTitle.includes(query);
    });
  }

  /**
   * Check if date filter is specified (both from and to dates are set)
   */
  get isDateSpecified(): boolean {
    return this.formState !== null &&
           this.formState.dateFilterFrom !== null &&
           this.formState.dateFilterTo !== null;
  }

  /**
   * Get date picker config with defaultDate set from formState
   */
  get datePickerConfig(): FlatpickrOptions {
    const config: FlatpickrOptions = { ...this.customYearOptions };
    
    // Set defaultDate if formState has date values
    if (this.isDateSpecified && this.formState) {
      const fromDateObj = new Date(this.formState.dateFilterFrom!);
      const toDateObj = new Date(this.formState.dateFilterTo!);

      // Create date strings in Y-m-d format from stored timestamps
      const fromDate = fromDateObj.toISOString().slice(0, 10);
      const toDate = toDateObj.toISOString().slice(0, 10);
      
      // For range mode, defaultDate can be an array of dates
      (config as any).defaultDate = [fromDate, toDate];
    }
    
    return config;
  }

  ngAfterViewChecked(): void {
    // Only manage date picker behavior on the quick filter tab
    if (this.activeDocumentTab !== 'locnhanh') {
      return;
    }

    const hasMode =
      this.formState !== null && this.formState.dateFilterMode !== null;

    if (this.yearPicker && hasMode) {
      const flatpickrInstance = (this.yearPicker as any).flatpickr;
      if (flatpickrInstance && this.formState) {
        const fromTimestamp = this.formState.dateFilterFrom;
        const toTimestamp = this.formState.dateFilterTo;

        try {
          if (this.isDateSpecified && !this.datePickerInitialized) {
            // Restore previously selected range
            const fromDate = new Date(fromTimestamp!).toISOString().slice(0, 10);
            const toDate = new Date(toTimestamp!).toISOString().slice(0, 10);
            flatpickrInstance.setDate([fromDate, toDate], false);
            this.datePickerInitialized = true;
          } else if (!this.isDateSpecified && !this.datePickerInitialized) {
            // First time selecting a mode with no dates yet: open picker
            flatpickrInstance.open();
            this.datePickerInitialized = true;
          }
        } catch {
          // Ignore errors if flatpickr is not ready
        }
      }
    }

    // Reset initialization flag when date picker or mode is cleared
    if (!this.yearPicker || !hasMode) {
      this.datePickerInitialized = false;
    }
  }

  onSearchChange(value: string): void {
    this.documentListSearch = value;
    this.searchChange.emit(value);
  }

  onSelectAllChange(checked: boolean): void {
    this.selectAllChange.emit(checked);
  }

  onDocumentSelectChange(item: DocumentListItem, checked: boolean): void {
    this.documentSelectChange.emit({ item, selected: checked });
  }

  onToggle(): void {
    this.toggle.emit();
  }

  onToggleFullTable(): void {
    this.toggleFullTable.emit();
  }

  onViewModeChange(value: string): void {
    this.viewModeChange.emit(value);
  }

  onMoiQuanHeChange(values: string[]): void {
    this.moiQuanHeSelectionChange.emit(values);
  }

  onCoQuanBanHanhChange(values: string[]): void {
    this.coQuanBanHanhSelectionChange.emit(values);
  }

  onLoaiVanBanChange(values: string[]): void {
    this.loaiVanBanSelectionChange.emit(values);
  }

  onTrangThaiHieuLucChange(values: string[]): void {
    this.trangThaiHieuLucSelectionChange.emit(values);
  }

  onYearChange(event: any): void {
    this.datePickerInitialized = true; // Mark as initialized after user changes date
    this.yearChange.emit(event);
  }

  onDateFilterModeChange(mode: 'ban_hanh' | 'hieu_luc', event: Event): void {
    this.dateFilterModeChange.emit({ mode, event });
  }

  onActiveTabChange(tabId: string): void {
    this.activeTabChange.emit(tabId);
  }

  onDocumentHover(documentId: string): void {
    this.documentHover.emit(documentId);
  }

  onDocumentHoverLeave(): void {
    this.documentHover.emit(null);
  }
}

