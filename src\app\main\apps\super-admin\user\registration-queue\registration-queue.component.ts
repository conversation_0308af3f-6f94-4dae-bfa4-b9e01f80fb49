import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { ColumnMode } from '@swimlane/ngx-datatable';
import { RegistrationQueueStatus } from '../user.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { OrganizationService } from '../../organization/organization.service';
import { formatRelativeTime } from '../../email/helpers';

@Component({
  selector: 'app-registration-queue',
  templateUrl: './registration-queue.component.html',
  styleUrls: ['./registration-queue.component.scss']
})
export class RegistrationQueueComponent implements OnInit {
  RegistrationQueueStatus = RegistrationQueueStatus;

  @ViewChild('registrationQueueTable') table: any;

  @Input() public status: RegistrationQueueStatus;
  @Input() public registrationQueue: any;
  @Input() public registrationQueuePage: any;
  @Input() public registrationQueueTotal: any;
  
  @Output() update: EventEmitter<{
    userId: any,
    status: string,
    organizationId: string,
    reason: string
  }> = new EventEmitter<{
    userId: any,
    status: string,
    organizationId: string,
    reason: string
  }>();

  @Output() registrationQueuePageChange: EventEmitter<number> = new EventEmitter<number>();
  
  public ColumnMode = ColumnMode;
  public listOrgan: any[] = [];
  public treeOrg: any[] = [];
  public approveForm: {
    userId: any,
    userFullname: string,
    userEmail: string,
    organizationId: string
  } = {
    userId: null,
    userFullname: '',
    userEmail: '',
    organizationId: ''
  };
  public tempSelectedOrganization: any = null;

  public rejectReason: string = 'Tài khoản ảo';
  public isOtherReasonChecked: boolean = false;

  constructor(
    private _modalService: NgbModal,
    private _organService: OrganizationService,
  ) { }

  formatRelativeTime = formatRelativeTime;

  rowDetailsToggleExpand(row) {
    this.table.rowDetail.toggleExpandRow(row);
  }

  onPage(event: any) {
    this.registrationQueuePage = event.offset + 1;
    this.registrationQueuePageChange.emit(this.registrationQueuePage);
  }

  updateRegistration(status: string) {
    const payload = {
      userId: this.approveForm.userId,
      status: status,
      organizationId: this.approveForm.organizationId,
      reason: this.rejectReason
    };
    this.update.emit(payload);
    this._modalService.dismissAll();
  }

  get selectedOrganizationName(): string {
    const org = this.listOrgan.find(o => o.id === this.approveForm.organizationId);
    return org ? org.name : 'Chưa có tổ chức';
  }

  modalOpen(modal, row: any | null = null) {
    if (row) {
      this.approveForm = {
        userId: row.user,
        userFullname: row.user_fullname,
        userEmail: row.user_email,
        organizationId: row.organization_id
      };
      this.tempSelectedOrganization = this.listOrgan.find(o => o.id === this.approveForm.organizationId);
    }

    this._modalService.open(modal, {
      centered: true,
    });
  }

  private loadOrganizations(): void {
    let params: any = {};
    this._organService.getAllOrganization(params).subscribe((res: any) => {
      this.listOrgan = res.map((org: any) => ({
        id: org.id,
        name: org.name,
        parent_organization: org.parent_organization,
        parent_organization_name: org.parent_organization_name
      }));

      const idMap: { [key: string]: any } = {};
      const tree: any[] = [];

      this.listOrgan.forEach(org => {
        idMap[org.id] = {
          ...org,
          children: []
        };
      });

      this.listOrgan.forEach(org => {
        const node = idMap[org.id];
        if (org.parent_organization) {
          if (idMap[org.parent_organization]) {
            idMap[org.parent_organization].children.push(node);
          }
        } else {
          tree.push(node);
        }
      });

      this.treeOrg = tree;
    });
  }

  shouldShowNode(node: any, search: string): boolean {
    if (!search) return true;
    const nodeName = (node.name || '').toLowerCase();
    const searchValue = search.toLowerCase();
    if (nodeName.includes(searchValue)) return true;
    if (Array.isArray(node.children)) {
      return node.children.some(child => this.shouldShowNode(child, search));
    }
    return false;
  }

  setTempOrganization(node: any): void {
    this.tempSelectedOrganization = node;
  }

  isTempSelected(node: any): boolean {
    if (!this.tempSelectedOrganization) return false;
    return this.tempSelectedOrganization.id === node.id;
  }

  confirmOrganization(modal: any, node?: any): void {
    if (node) {
      this.tempSelectedOrganization = node;
    }
    if (!this.tempSelectedOrganization) return;
    this.approveForm.organizationId = this.tempSelectedOrganization.id;
    modal.close();
  }

  ngOnInit(): void {
    this.loadOrganizations();
  }

}
