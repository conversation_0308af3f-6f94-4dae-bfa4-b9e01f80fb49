.document-list-container {
  --document-list-panel-width: 30rem;
  position: absolute;
  left: 8px;
  top: 8px;
  z-index: 10;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: auto;

  .document-list-panel {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    width: 0;
    max-width: var(--document-list-panel-width);
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transition: width 220ms ease, opacity 220ms ease, transform 220ms ease;
    opacity: 0;
    transform: translateX(-6px);

    .document-list-header {
      padding: 8px 10px 0 10px;
      border-bottom: 1px solid #eee;
      align-items: flex-start !important;
    }

    .close-btn {
      appearance: none;
      border: none;
      background: transparent;
      color: #6c757d;
      font-size: 20px;
      line-height: 1;
      cursor: pointer;
      padding: 2px 6px;
      border-radius: 4px;

      &:hover {
        background: rgba(0, 0, 0, 0.06);
        color: #343a40;
      }

      &:focus {
        outline: 2px solid rgba(13, 110, 253, 0.4);
        outline-offset: 2px;
      }
    }
  }

  .toggle-icon {
    width: 24px;
    height: 24px;
    margin-left: 8px;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.1);
    }
  }

  &.expanded {
    .document-list-panel {
      width: var(--document-list-panel-width);
      max-width: var(--document-list-panel-width);
      opacity: 1;
      transform: translateX(0);
    }
  }
}

.document-row--highlighted {
  background-color: rgba(40, 120, 240, 0.12) !important;
}

.nav-documentlist-container {
  overflow-y: auto;
  max-height: 40vh;
}

.document-list-table-wrapper {
  padding: 8px;
}

.document-list-search-container {
  margin-bottom: 8px;
}

.document-list-table {
  width: 100%;
  border-collapse: collapse;

  thead {
    tr {
      th {
        padding: 8px;
        font-size: 12px;
        font-weight: 600;
        text-align: left;
        border-bottom: 1px solid #e0e0e0;

        .custom-control-label {
          cursor: pointer;
          user-select: none;
        }
      }
    }
  }

  tbody {
    tr {
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(40, 120, 240, 0.08);
      }

      td {
        padding: 8px;
        font-size: 13px;
        border-bottom: 1px solid #f0f0f0;

        .custom-control-label {
          cursor: pointer;
          user-select: none;
        }
      }
    }
  }
}

.show-button-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .show-button-label {
    font-size: 12px;
    color: #2878f0;
  }

  img {
    width: 16px;
    height: 16px;
  }
}

// Styles for "Lọc theo thời gian" section radio buttons
.document-list-container {
  .date-filter-content {
    .date-filter-radio {
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      .custom-control-label {
        font-size: 1rem !important;
      }
    }
  }
}