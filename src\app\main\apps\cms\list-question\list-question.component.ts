import {
  Component,
  OnInit,
  ViewEncapsulation,
  OnDestroy,
  Input,
  OnChanges,
  Output,
  EventEmitter,
  SimpleChanges,
  ChangeDetectorRef,
  ViewChild,
} from '@angular/core';
import { ColumnMode } from '@swimlane/ngx-datatable';
import { formatRelativeTime } from '../../super-admin/email/helpers';
import Swal from 'sweetalert2';
import { ToastrService } from 'ngx-toastr';
import { CmsService } from '../cms.service';
import { AuthenticationService } from 'app/auth/service';

@Component({
  selector: 'app-list-question',
  templateUrl: './list-question.component.html',
  styleUrls: ['./list-question.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ListQuestionComponent implements OnInit, OnDestroy, OnChanges {
  @ViewChild('QaTable') table: any;
  
  @Input() public qa: any;
  @Input() public writerUsers: any;
  @Input() public reviewerUsers: any;
  @Input() public adminUsers: any;

  @Output() createdOrUpdated: EventEmitter<any> = new EventEmitter<any>();

  public activeTabId = 'ALL';

  public ColumnMode = ColumnMode;
  public totalItem: number = 0;
  public page: number = 0;

  public filteredQa: any[] = [];
  public isWriter = false;
  public isReviewer = false;
  public isAdmin = false;

  private getMyUserId(): number | null {
    const me = (this as any)._auth?.currentUserValue;
    return me?.id ?? null;
  }

  private getEntityId(entity: any): number | null {
    if (entity == null) return null;
    if (typeof entity === 'number') return entity;
    if (typeof entity === 'object') {
      return entity.id ?? entity.user?.id ?? entity.user_info?.id ?? entity.user_id ?? null;
    }
    return null;
  }

  private computeRoles() {
    const myId = this.getMyUserId();
    const hasMe = (arr: any[]) =>
      Array.isArray(arr) && arr.some(u => {
        const id = this.getEntityId(u?.user ?? u?.user_info ?? u);
        return id === myId;
      });

    this.isWriter   = hasMe(this.writerUsers || []);
    this.isReviewer = hasMe(this.reviewerUsers || []);
    this.isAdmin    = hasMe(this.adminUsers || []);
  }

  private applyWriterScope(list: any[]): any[] {
    if (this.isAdmin || this.isReviewer) return list;
    if (this.isWriter) {
      const myId = this.getMyUserId();
      if (!myId) return [];
      return list.filter(item => this.getEntityId(item?.writer) === myId);
    }
    return list;
  }

  private readonly RESTRICTED: string[] = ['DRAFT','WRITING','REVIEWING'];

  private applyWriterScopeForStatus(list: any[], status: string): any[] {
    if (this.isAdmin || this.isReviewer) return list;
    if (!this.isWriter) return list;

    const myId = this.getMyUserId();
    if (!myId) return [];

    if (status === 'ALL') {
      return list.filter(it => 
        this.RESTRICTED.includes(it?.status) ? (this.getEntityId(it?.writer) === myId) : true
      );
    }
    if (this.RESTRICTED.includes(status)) {
      return list.filter(it => this.getEntityId(it?.writer) === myId);
    }
    return list;
  }

  constructor(
    private _toastSerive: ToastrService,
    private _cmsService: CmsService,
    private cdr: ChangeDetectorRef,
    private _auth: AuthenticationService,
  ) { }

  formatRelativeTime = formatRelativeTime;

  get draftQa() {
    const base = (this.qa ?? []).filter(item => item.status === 'DRAFT');
    return this.applyWriterScope(base);
  }
  get writingQa() {
    const base = (this.qa ?? []).filter(item => item.status === 'WRITING');
    return this.applyWriterScope(base);
  }
  get reviewingQa() {
    const base = (this.qa ?? []).filter(item => item.status === 'REVIEWING');
    return this.applyWriterScope(base);
  }

  get completedQa() {
    const base = (this.qa ?? []).filter(it => it.status === 'COMPLETED');
    return this.applyWriterScope(base);
  }
  get publishedQa() {
    const base = (this.qa ?? []).filter(it => it.status === 'PUBLISHED');
    return this.applyWriterScope(base);
  }
  get unpublishedQa() {
    const base = (this.qa ?? []).filter(it => it.status === 'UNPUBLISHED');
    return this.applyWriterScope(base);
  }
  get rejectedQa() {
    const base = (this.qa ?? []).filter(it => it.status === 'REJECTED');
    return this.applyWriterScope(base);
  }
  get archivedQa() {
    const base = (this.qa ?? []).filter(it => it.status === 'ARCHIVED');
    return this.applyWriterScope(base);
  }

  onActivate(event) {}

  onPage(event) {}

  onSort(event) {}

  onTabChange(status: string) {
    this.table.offset = 0;
    this.getFilteredQa(status);
  }

  getQaTitle(row: any): string {
    if (!row) return '';

    const pick = (...vals: any[]) =>
      vals.map(v => (typeof v === 'string' ? v.trim() : (v ?? '')))
          .find(v => typeof v === 'string' && v.length > 0);

    const fromMeta = typeof row.meta === 'object' ? (row.meta?.title ?? '') : '';
    const title = pick(
      row.title, row.name, row.subject,
      row.task_title, row.work_title, row.job_title,
      row.heading, row.qa_title, fromMeta
    );

    if (title) return title;

    const plain = String(row.question || '')
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    if (!plain) return '(không có tiêu đề)';
    const firstSentence = plain.split(/(?<=[.!?])\s+/)[0] || plain;
    return firstSentence;
  }
  compareNumber = (a: any, b: any) => (Number(a) || 0) - (Number(b) || 0);
  get allQaCount(): number {
    return this.applyWriterScope(this.qa || []).length;
  }
  getFilteredQa(status: string) {
    let list = Array.isArray(this.qa) ? [...this.qa] : [];
    if (status !== 'ALL') list = list.filter(item => item.status === status);

    list = this.applyWriterScope(list);
    this.filteredQa = list.map(it => ({
      ...it,
      __qa_title__: this.getQaTitle(it)
    }));
    // this.filteredQa = list;
    this.totalItem = this.filteredQa.length;
    this.page = 1;
    this.cdr.markForCheck();
  }

  updateActiveQa(id) {
    const data = null;
    this._cmsService.updateActiveQa(id, data).subscribe(
      (res) => {
        this.createdOrUpdated.emit(res);
        this._toastSerive.success("Đã cập nhật câu hỏi", "Thành công")
      },
      (err) => {
        this._toastSerive.error("Có lỗi xảy ra khi cập nhật câu hỏi", "Lỗi")
      }
    );
  }

  deleteQa(id) {
    Swal.fire({
      title: "Bạn chắc chắn muốn xóa?",
      text: "Hành động này không thể hoàn tác",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
      confirmButtonColor: "#d33",
    }).then((result) => {
      if (result.isConfirmed) {
        this._cmsService.deleteQa(id).subscribe(
          (res) => {
            this.createdOrUpdated.emit(res);
            this._toastSerive.success("Đã xoá câu hỏi", "Thành công")
          },
          (err) => {
            this._toastSerive.error("Có lỗi xảy ra khi xóa câu hỏi", "Lỗi")
          }
        );
      }
    });
  }

  ngOnInit(): void {
    this.computeRoles();
    this.getFilteredQa(this.activeTabId);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if ('writerUsers' in changes || 'reviewerUsers' in changes || 'adminUsers' in changes) {
      this.computeRoles();
    }
    if ('qa' in changes) {
      this.getFilteredQa(this.activeTabId);
    }
  }

  ngOnDestroy(): void {}

}
