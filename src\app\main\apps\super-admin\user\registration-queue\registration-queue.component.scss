// Utilities for org picker
.input-with-icon {
    position: relative;
  }
  
  .btn-icon-inside {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: transparent;
    padding: 0;
    cursor: pointer;
  }
  
  .btn-icon-inside img.dl-icon {
    width: 16px;
    height: 16px;
  }
  
  .input-with-icon:has(.left-icon) .form-control {
    padding-left: 40px;
  }
  
  .input-with-icon:has(.right-icon) .form-control {
    padding-right: 40px;
  }
  
  .btn-icon-inside.left-icon {
    left: 10px;
  }
  
  .btn-icon-inside.right-icon {
    right: 10px;
  }
  
  .tree-row {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 42px;
    padding: 8px 12px;
    padding-right: 50px;         
    border-bottom: 1px solid #f2f4f7;
    white-space: normal;          
  }
  .checkbox-right {
    position: absolute;
    right: 20px; 
    top: 50%;
    transform: translateY(-50%);
  }
  .checkbox-right .form-check-input {
    width: 1.05rem;
    height: 1.05rem;
    margin: 0; 
    cursor: pointer;
  }
  .tree-row {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 42px;
    padding: 8px 12px;
    border-bottom: 1px solid #f2f4f7;
    transition: background-color 120ms ease, color 120ms ease;
  }
  .tree-row:last-child {
    border-bottom: 0;
  }
  .tree-row:hover {
    background: #f8fafc; 
  }
  
  .toggle {
    width: 28px;
    height: 28px;
    border: 0;
    background: transparent;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    opacity: 0.85;
    cursor: pointer;
  }
  .toggle:hover {
    background: #eef2ff;
    opacity: 1;
  }
  
  .node-icon {
    width: 18px;
    opacity: 0.9;
  }
  .node-text {
    flex: 1 1 auto;
    min-width: 0;      
  }
  .node-name {
    overflow-wrap: anywhere;      
    word-break: break-word;    
  }
  .node-count {
    font-size: 12px;
    color: #8a8f98;
  }
  
  .form-check-input {
    width: 1.05rem;
    height: 1.05rem;
    cursor: pointer;
  }
  
  .tree-row.has-children .node-icon {
    opacity: 1;
  }
  
  .tree-row.is-partial {
    background: linear-gradient(
      0deg,
      rgba(99, 102, 241, 0.06),
      rgba(99, 102, 241, 0.06)
    );
  }
  
  .tree-wrap {
    width: 100%;
    max-height: clamp(320px, 60vh, 640px);
    overflow-y: auto;    
    overflow-x: hidden;  
    border: 1px solid #e9ecef;
    border-radius: 12px;
    background: #fff;
  }
  
  .tree-wrap::-webkit-scrollbar { width: 8px; }
  .tree-wrap::-webkit-scrollbar-thumb { background: #e0e3e7; border-radius: 8px; }
  .tree-wrap::-webkit-scrollbar-thumb:hover { background: #cbd0d6; }