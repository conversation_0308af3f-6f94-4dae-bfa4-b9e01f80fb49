import { Component, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';

@Component({
  selector: 'app-landing-footer',
  templateUrl: './landing-footer.component.html',
  styleUrls: ['./landing-footer.component.scss'],
  encapsulation: ViewEncapsulation.Emulated 
})
export class LandingFooterComponent {
  /**
   * URL “Hướng dẫn sử dụng” (url1 của landing)
   * Dùng: <app-landing-footer [url1]="urls?.url1"></app-landing-footer>
   */
  @Input() url1: string | undefined;

  /**
   * N<PERSON>u anh muốn bắt sự kiện subscribe ở cha:
   * <app-landing-footer (subscribeEmail)="handle($event)"></app-landing-footer>
   */
  @Output() subscribeEmail = new EventEmitter<string>();

  /**
   * Bắt submit form trong footer.
   * Sửa lỗi: Property 'onSubscribeSubmit' does not exist on type 'LandingFooterComponent'.
   */
  onSubscribeSubmit(event: Event): void {
    event.preventDefault();
    const form = event.target as HTMLFormElement;
    const data = new FormData(form);
    const email = String(data.get('email') || '').trim();
    if (!email) return;

    // Bắn lên cha nếu muốn xử lý tiếp (ghi log, gọi API...)
    this.subscribeEmail.emit(email);

    // Reset form tại chỗ cho UX
    form.reset();
  }
}
