import { Injectable } from '@angular/core';
import { GraphFormState, GraphRequestBody, DateFilterMode } from '../types/graph.types';
import { VALIDATION_LIMITS } from '../constants/graph.constants';
import { safeArray } from '../helper/helper';

/**
 * Service for managing graph form state and validation
 */
@Injectable({
  providedIn: 'root'
})
export class GraphFormService {
  /**
   * Validate number input with min/max constraints
   */
  validateNumberInput(
    formState: GraphFormState,
    value: number,
    min: number,
    max: number,
    propertyName: keyof GraphFormState,
    fieldName: string
  ): { isValid: boolean; error: string; value?: number } {
    const errorProperty = `${propertyName}Error` as keyof GraphFormState;
    (formState as any)[errorProperty] = '';

    if (value < min) {
      const error = `${fieldName} tối thiểu là ${min}`;
      (formState as any)[errorProperty] = error;
      return { isValid: false, error };
    } else if (value > max) {
      const error = `${fieldName} tối đa là ${max}`;
      (formState as any)[errorProperty] = error;
      return { isValid: false, error };
    } else {
      (formState as any)[propertyName] = value;
      return { isValid: true, error: '', value };
    }
  }

  /**
   * Validate depth input
   */
  validateDepth(formState: GraphFormState, value: number): { isValid: boolean; error: string; value?: number } {
    return this.validateNumberInput(
      formState,
      value,
      VALIDATION_LIMITS.DEPTH.MIN,
      VALIDATION_LIMITS.DEPTH.MAX,
      'depth',
      'Số cấp liên kết'
    );
  }

  /**
   * Validate global limit input
   */
  validateGlobalLimit(formState: GraphFormState, value: number): { isValid: boolean; error: string; value?: number } {
    return this.validateNumberInput(
      formState,
      value,
      VALIDATION_LIMITS.GLOBAL_LIMIT.MIN,
      VALIDATION_LIMITS.GLOBAL_LIMIT.MAX,
      'global_limit',
      'Giới hạn số lượng tài liệu'
    );
  }

  /**
   * Validate limit per seed input
   */
  validateLimitPerSeed(formState: GraphFormState, value: number): { isValid: boolean; error: string; value?: number } {
    return this.validateNumberInput(
      formState,
      value,
      VALIDATION_LIMITS.LIMIT_PER_SEED.MIN,
      VALIDATION_LIMITS.LIMIT_PER_SEED.MAX,
      'limit_per_seed',
      'Giới hạn số lượng tài liệu cho mỗi nhánh'
    );
  }

  /**
   * Parse year range from date input
   */
  changeYear(event: any, formState: GraphFormState): void {
    const inputElement = event.target as HTMLInputElement;
    const selectedValue = inputElement.value;

    if (!formState) return;

    if (selectedValue) {
      let dates: string[] = [];

      // Check for Vietnamese separator first
      if (selectedValue.includes(' đến ')) {
        dates = selectedValue.split(' đến ');
      }
      // Check for English separator
      else if (selectedValue.includes(' to ')) {
        dates = selectedValue.split(' to ');
      }
      // Check for other possible separators
      else if (selectedValue.includes(' - ')) {
        dates = selectedValue.split(' - ');
      }

      // Only treat as a valid range when we clearly have two dates
      if (dates.length === 2) {
        const fromDate = new Date(dates[0]);
        const toDate = new Date(dates[1]);

        if (!isNaN(fromDate.getTime()) && !isNaN(toDate.getTime())) {
          // Store full timestamps so we can filter by exact date,
          // not just by year.
          formState.dateFilterFrom = fromDate.getTime();
          formState.dateFilterTo = toDate.getTime();
        } else {
          formState.dateFilterFrom = formState.dateFilterTo = null;
        }
      } else {
        // While the user is still picking (only one date selected), keep the range "incomplete"
        formState.dateFilterFrom = formState.dateFilterTo = null;
      }
    } else {
      formState.dateFilterFrom = formState.dateFilterTo = null;
    }
  }

  /**
   * Build date filter payload for API request
   */
  buildDateFilterPayload(formState: GraphFormState | null): {
    ban_hanh_year_from: number | null;
    ban_hanh_year_to: number | null;
    hieu_luc_year_from: number | null;
    hieu_luc_year_to: number | null;
  } {
    if (!formState) {
      return {
        ban_hanh_year_from: null,
        ban_hanh_year_to: null,
        hieu_luc_year_from: null,
        hieu_luc_year_to: null,
      };
    }

    const from =
      formState.dateFilterFrom !== null && formState.dateFilterFrom !== undefined
        ? new Date(formState.dateFilterFrom).getFullYear()
        : null;
    const to =
      formState.dateFilterTo !== null && formState.dateFilterTo !== undefined
        ? new Date(formState.dateFilterTo).getFullYear()
        : null;
    const mode = formState.dateFilterMode;

    return {
      ban_hanh_year_from: mode === 'ban_hanh' ? from : null,
      ban_hanh_year_to: mode === 'ban_hanh' ? to : null,
      hieu_luc_year_from: mode === 'hieu_luc' ? from : null,
      hieu_luc_year_to: mode === 'hieu_luc' ? to : null,
    };
  }

  /**
   * Build complete request body for graph API
   */
  buildRequestBody(formState: GraphFormState, nodeId: string): GraphRequestBody {
    const dateFilterPayload = this.buildDateFilterPayload(formState);

    return {
      co_quan_ban_hanh: safeArray(formState.selectedCoQuanBanHanh),
      depth: formState.depth || 3,
      global_limit: formState.global_limit || 25,
      limit_per_seed: formState.limit_per_seed || 5,
      loai_van_ban: safeArray(formState.selectedBoLocLoaiVanBan),
      node_ids: [nodeId],
      relationship_types: safeArray(formState.selectedBoLocMoiQuanHe),
      target_node_type: formState.search_legal_term || 'ALL',
      tinh_trang_hieu_luc: safeArray(formState.selectedTinhTrangHieuLuc),
      ...dateFilterPayload,
    };
  }

  /**
   * Build default request body without filters (only target_node_type, depth, limits)
   * Used when switching view modes to fetch all data, then apply frontend filters
   */
  buildDefaultRequestBody(targetNodeType: string, nodeId: string, depth?: number, globalLimit?: number, limitPerSeed?: number): GraphRequestBody {
    return {
      co_quan_ban_hanh: [],
      depth: depth || 1,
      global_limit: globalLimit || 25,
      limit_per_seed: limitPerSeed || 25,
      loai_van_ban: [],
      node_ids: [nodeId],
      relationship_types: [],
      target_node_type: targetNodeType || 'ALL',
      tinh_trang_hieu_luc: [],
      ban_hanh_year_from: null,
      ban_hanh_year_to: null,
      hieu_luc_year_from: null,
      hieu_luc_year_to: null,
    };
  }

  /**
   * Build date filter payload from a specific form state
   */
  buildDateFilterPayloadFromState(state: GraphFormState): {
    ban_hanh_year_from: number | null;
    ban_hanh_year_to: number | null;
    hieu_luc_year_from: number | null;
    hieu_luc_year_to: number | null;
  } {
    const from =
      state.dateFilterFrom !== null && state.dateFilterFrom !== undefined
        ? new Date(state.dateFilterFrom).getFullYear()
        : null;
    const to =
      state.dateFilterTo !== null && state.dateFilterTo !== undefined
        ? new Date(state.dateFilterTo).getFullYear()
        : null;
    const mode = state.dateFilterMode;

    return {
      ban_hanh_year_from: mode === 'ban_hanh' ? from : null,
      ban_hanh_year_to: mode === 'ban_hanh' ? to : null,
      hieu_luc_year_from: mode === 'hieu_luc' ? from : null,
      hieu_luc_year_to: mode === 'hieu_luc' ? to : null,
    };
  }
}

