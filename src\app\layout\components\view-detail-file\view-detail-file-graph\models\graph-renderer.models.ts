import { GraphNode, GraphLink } from '../types/graph.types';
import { ApiNode } from '../types/graph.types';

/**
 * D3-specific graph node interface
 */
export interface D3GraphNode extends GraphNode {
  data?: ApiNode | null;
  symbolSize?: number;
  opacity?: number;
  isBlurred?: boolean;
  x?: number;
  y?: number;
  vx?: number;
  vy?: number;
  fx?: number | null;
  fy?: number | null;
}

/**
 * D3-specific graph link interface
 */
export interface D3GraphLink {
  id: string;
  label?: string;
  strength?: number;
  __curveness?: number;
  __isBaiBo?: boolean;
  __relationshipType?: string;
  source: D3GraphNode | string | number;
  target: D3GraphNode | string | number;
  sourceId: string;
  targetId: string;
  highlighted: boolean;
  opacity: number;
}

/**
 * Renderer event callbacks
 */
export interface GraphRendererCallbacks {
  onNodeClick?: (nodeId: string, apiNode: ApiNode) => void;
  onNodeRightClick?: (event: MouseEvent, nodeId: string, apiNode: ApiNode) => void;
  onNodeDoubleClick?: (nodeId: string, apiNode: ApiNode) => void;
  onNodeDrag?: (nodeId: string, x: number, y: number) => void;
  formatTooltip?: (nodeId: string, apiNode: ApiNode | null, label: string) => string;
}

