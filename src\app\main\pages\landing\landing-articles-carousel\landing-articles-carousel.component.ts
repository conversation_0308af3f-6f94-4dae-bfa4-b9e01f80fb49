import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  HostListener,
  OnInit,
  SimpleChanges,
} from "@angular/core";
import { Article } from "./landing-article/landing-article.component";

@Component({
  selector: "app-landing-articles-carousel",
  templateUrl: "./landing-articles-carousel.component.html",
  styleUrls: ["./landing-articles-carousel.component.scss"],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LandingArticlesCarouselComponent implements OnInit, OnChanges {
  @Input() articles: Article[] = [];
  @Input() itemsPerSlide = 4;
  @Input() autoPlayIntervalMs = 0;

  slides: Article[][] = [];
  activeId = "s-0";
  private responsiveItems = 4;
  private winW = window.innerWidth;
  get isDesktop(): boolean { return this.winW >= 1200; }

  ngOnInit(): void {
    this.responsiveItems = this.getItemsPerSlide(window.innerWidth);
    this.rebuildSlides();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes["articles"] || changes["itemsPerSlide"]) {
      this.responsiveItems = this.getItemsPerSlide(window.innerWidth);
      this.rebuildSlides();
    }
  }
   
  @HostListener('window:resize')
  onResize(): void {
    this.winW = window.innerWidth;
    const newSize = this.getItemsPerSlide(window.innerWidth);
    if (newSize !== this.responsiveItems) {
      this.responsiveItems = newSize;
      this.rebuildSlides();
    }
  }

  /** Desktop: 4; Mobile (≤575): 1. Các dải khác vẫn 4 để ra bố cục 2x2 (nhờ SCSS 2 cột). */
  private getItemsPerSlide(w: number): number {
    return w <= 575 ? 1 : (this.itemsPerSlide || 4);
  }

private rebuildSlides(): void {
  const list = this.articles ?? [];
  const size = Math.max(1, Math.floor(this.responsiveItems) || 1);

  if (list.length === 0) {
    this.slides = [];
    this.activeId = 's-0';
    return;
  }
  if (list.length <= size) {
    this.slides = [list.slice()];
    this.activeId = 's-0';
    return;
  }

  // cắt mảng theo size
  const chunks: Article[][] = [];
  for (let i = 0; i < list.length; i += size) {
    chunks.push(list.slice(i, i + size));
  }

  // slide cuối thiếu → DUPLICATE từ slide trước (không làm hụt slide trước)
  if (chunks.length > 1) {
    const lastIdx = chunks.length - 1;
    const last = chunks[lastIdx];
    const prev = chunks[lastIdx - 1];

    if (last.length < size) {
      const need = size - last.length;
      const borrow = prev.slice(Math.max(0, prev.length - need)); // bản sao
      // ghép vào đầu slide cuối để thứ tự xem vẫn tự nhiên
      last.unshift(...borrow);
    }
  }

  this.slides = chunks;
  this.activeId = 's-0';
}
ghosts(len: number): number[] {
  const need = Math.max(0, 4 - (len ?? 0));
  return Array(need).fill(0);
}
ghostsLeft(len: number): number[] {
  const missing = Math.max(0, 4 - (len ?? 0));
  return Array(Math.floor(missing / 2)).fill(0);
}
ghostsRight(len: number): number[] {
  const missing = Math.max(0, 4 - (len ?? 0));
  return Array(Math.ceil(missing / 2)).fill(0);
}

  setActive(index: number): void {
    this.activeId = `s-${index}`;
  }

  trackByArticle = (_: number, a: Article) => a?.id ?? a?.slug ?? _;
}
