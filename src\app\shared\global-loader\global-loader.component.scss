.gl{
  position: fixed; inset: 0; z-index: 2147483000;
  opacity: 0; pointer-events: none; transition: opacity .18s ease;
}
.gl.is-active{ opacity: 1; pointer-events: auto; }

/* nền mượt: gradient + blur */
.gl__bg{
  position: absolute; inset: 0;
  background:
    radial-gradient(60% 70% at 20% 10%, rgba(14,165,233,.20), transparent 65%),
    radial-gradient(70% 60% at 80% 30%, rgba(59,130,246,.18), transparent 70%),
    radial-gradient(80% 80% at 50% 90%, rgba(2, 18, 43, .22), transparent 70%),
    linear-gradient(180deg, #f7fbff 0%, #ffffff 100%);
  backdrop-filter: blur(4px);
}

/* nội dung giữa màn hình */
.gl__content{
  position: absolute; inset: 0;
  display: grid; place-items: center;
  gap: 16px;
}

.gl__logo{ width: 200px; height: auto; filter: drop-shadow(0 4px 10px rgba(2,18,43,.12)); }

/* spinner đ<PERSON><PERSON> gi<PERSON>n */
.gl__spinner{
  width: 36px; height: 36px; border-radius: 999px;
  border: 3px solid rgba(2,18,43,.12);
  border-top-color: #0EA5E9;
  animation: glspin 900ms linear infinite;
}
@keyframes glspin { to { transform: rotate(360deg); } }

.gl__hint{ color: #334155; font-weight: 600; letter-spacing: .2px; }
