import { Component, OnInit } from "@angular/core";
import { LoadingOverlayRef } from "../loading.service";

@Component({
  selector: "app-spinner",
  templateUrl: "./spinner.component.html",
  styleUrls: ["./spinner.component.scss"],
})
export class SpinnerComponent implements OnInit {
  public timeShowButtonCancel: boolean = false;
  constructor(private overlayRef: LoadingOverlayRef) {}

  ngOnInit(): void {
    setTimeout(() => {
      this.timeShowButtonCancel = true;
    }, 5000);
  }
  cancelLoading() {
    this.overlayRef.close();
  }
}
