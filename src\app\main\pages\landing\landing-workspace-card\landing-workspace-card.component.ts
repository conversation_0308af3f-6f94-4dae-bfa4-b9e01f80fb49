import { Component, Input } from "@angular/core";

export interface WorkspaceFeature {
  id: string;
  title: string;
  description: string;
  imageSrc: string;
  imageAlt: string;
}

@Component({
  selector: "app-landing-workspace-card",
  templateUrl: "./landing-workspace-card.component.html",
  styleUrls: ["./landing-workspace-card.component.scss"],
})
export class LandingWorkspaceCardComponent {
  @Input() features: WorkspaceFeature[] = [];
}
