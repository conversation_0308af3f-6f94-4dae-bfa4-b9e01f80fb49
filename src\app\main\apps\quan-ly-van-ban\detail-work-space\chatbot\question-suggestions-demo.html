<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Question Suggestions UI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .chat-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .message {
            margin-bottom: 20px;
        }
        
        .user-message {
            background: #007bff;
            color: white;
            padding: 12px 16px;
            border-radius: 18px;
            margin-left: auto;
            max-width: 70%;
            text-align: right;
        }
        
        .bot-message {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            max-width: 85%;
        }
        
        /* Question Suggestions Styles - Copy từ SCSS */
        .question-suggestions {
            margin-top: 16px;
            padding: 16px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .suggestions-title {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .suggestions-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .suggestion-item {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px 16px;
            text-align: left;
            font-size: 14px;
            color: #495057;
            cursor: pointer;
            transition: all 0.2s ease;
            line-height: 1.4;
        }

        .suggestion-item:hover {
            background-color: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .suggestion-item:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <h2>Demo: Question Suggestions UI</h2>
        
        <div class="message">
            <div class="user-message">
                Thủ tục ly hôn đơn phương được thực hiện như thế nào?
            </div>
        </div>
        
        <div class="message">
            <div class="bot-message">
                <p><strong>Trả lời:</strong></p>
                <p>Thủ tục ly hôn đơn phương được thực hiện theo quy định tại Luật Hôn nhân và Gia đình 2014. Người yêu cầu ly hôn cần nộp đơn tại Tòa án có thẩm quyền với các giấy tờ cần thiết...</p>
                
                <!-- Question Suggestions -->
                <div class="question-suggestions">
                    <p class="suggestions-title">Các câu hỏi khác cùng chủ đề đã được vấn bởi Chuyên gia pháp luật</p>
                    <div class="suggestions-list">
                        <button class="suggestion-item" onclick="selectQuestion(this)">
                            Điều kiện để được ly hôn đơn phương là gì?
                        </button>
                        <button class="suggestion-item" onclick="selectQuestion(this)">
                            Quy định về quyền nuôi con sau khi ly hôn như thế nào?
                        </button>
                        <button class="suggestion-item" onclick="selectQuestion(this)">
                            Thủ tục chia tài sản chung khi ly hôn được thực hiện ra sao?
                        </button>
                        <button class="suggestion-item" onclick="selectQuestion(this)">
                            Có thể ly hôn khi một bên không đồng ý không?
                        </button>
                        <button class="suggestion-item" onclick="selectQuestion(this)">
                            Thời gian xử lý hồ sơ ly hôn mất bao lâu?
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function selectQuestion(button) {
            const question = button.textContent;
            alert(`Đã chọn câu hỏi: "${question}"\n\nTrong ứng dụng thực tế, câu hỏi này sẽ được gửi tự động như một câu hỏi mới.`);
        }
    </script>
</body>
</html>
