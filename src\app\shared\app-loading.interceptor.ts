import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON>vent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable, finalize, timer, switchMap } from 'rxjs';
import { LoadingService } from './loading.service';

@Injectable()
export class AppLoadingInterceptor implements HttpInterceptor {
  constructor(private loader: LoadingService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    let counted = false;
    // chỉ tính sau 200ms
    // const startDelay$ = timer(100).pipe(switchMap(() => {
    //   this.loader.incPending(); counted = true;
    //   return next.handle(req);
    // }));

    return next.handle(req).pipe(
      finalize(() => { if (counted) this.loader.decPending(); })
    );
  }
}
