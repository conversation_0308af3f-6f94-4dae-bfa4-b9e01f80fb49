import { Component, ElementRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import * as XLSX from 'xlsx';
import { UserService } from '../user.service';
import { OrganizationService } from '../../organization/organization.service';
import { AuthenticationService } from 'app/auth/service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FlatpickrOptions } from 'ng2-flatpickr';

interface UserFromExcel {
  email: string;
  password: string;
  fullname: string;
  phone: string;
  gender: 'Nam' | 'Nữ' | '';
  dob: string; // YYYY-MM-DD
  isValid: boolean;
  errors?: string[];
}

@Component({
  selector: 'app-multiple-user-control',
  templateUrl: './multiple-user-control.component.html',
  styleUrls: ['./multiple-user-control.component.scss']
})
export class MultipleUserControlComponent implements OnInit {
  @ViewChild('organizationModal') organizationModal!: TemplateRef<any>;
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;
  
  public selectedFile: File | null = null;
  public isDragOver = false;
  public isProcessing = false;
  public usersFromExcel: UserFromExcel[] = [];
  public errorMessage = '';
  public warningMessage = '';
  public successMessage = '';
  public skippedEmails: any[] = [];

  public rootOrgId: any = null;
  public treeOrg: any[] = [];
  public listOrganization: any[] = [];
  public selectedOrganization: any;
  public tempSelectedOrganization: any = null;

  public basicDateOptions: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
    mode: "single",
    maxDate: "today",
  };

  constructor(
    private modalService: NgbModal,
    private _userService: UserService,
    private _organService: OrganizationService,
    private _authenService: AuthenticationService
  ) { 
    const role = _authenService.currentUserValue.role;
    if (role == "ADMIN") {
      this.rootOrgId = localStorage.getItem("organization_id");
    }
  }

  ngOnInit(): void {
    this.loadOrganizations();
  }

  downloadTemplate() {
    const link = document.createElement('a');
    link.href = 'assets/cls_template_users.xlsx';
    link.download = 'template_nguoi_dung.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  openOrganizationPicker(): void {
    // initialize temp selection with current selection when opening
    this.tempSelectedOrganization = this.selectedOrganization || null;
    this.modalService.open(this.organizationModal, { size: 'lg', centered: true });
  }

  setTempOrganization(node: any): void {
    this.tempSelectedOrganization = node;
  }

  isTempSelected(node: any): boolean {
    if (!this.tempSelectedOrganization) return false;
    return this.tempSelectedOrganization.id === node.id;
  }

  shouldShowNode(node: any, search: string): boolean {
    if (!search) return true;
    const nodeName = (node.name || '').toLowerCase();
    const searchValue = search.toLowerCase();
    if (nodeName.includes(searchValue)) return true;
    if (Array.isArray(node.children)) {
      return node.children.some(child => this.shouldShowNode(child, search));
    }
    return false;
  }

  confirmOrganization(modal: any, node?: any): void {
    if (node) {
      this.tempSelectedOrganization = node;
    }
    if (!this.tempSelectedOrganization) return;
    this.selectedOrganization = this.tempSelectedOrganization;
    if (this.selectedOrganization) {
      this.errorMessage = ""
    }
    modal.close();
  }

  private loadOrganizations(): void {
    let params: any = {};
    if (this.rootOrgId) {
      params.root_organization_id = this.rootOrgId;
    }
    this._organService.getAllOrganization(params).subscribe((res: any) => {
      this.listOrganization = res.map((org: any) => ({
        id: org.id,
        name: org.name,
        parent_organization: org.parent_organization,
        parent_organization_name: org.parent_organization_name
      }));

      const idMap: { [key: string]: any } = {};
      const tree: any[] = [];

      this.listOrganization.forEach(org => {
        idMap[org.id] = {
          ...org,
          children: []
        };
      });

      this.listOrganization.forEach(org => {
        const node = idMap[org.id];
        if (org.parent_organization) {
          if (idMap[org.parent_organization]) {
            idMap[org.parent_organization].children.push(node);
          }
        } else {
          tree.push(node);
        }
      });

      this.treeOrg = tree;
    });
  }

  private createEmptyUser(): UserFromExcel {
    return {
      email: '',
      password: '',
      fullname: '',
      phone: '',
      gender: '',
      dob: '',
      isValid: false,
      errors: ['Chưa nhập dữ liệu']
    };
  }

  addUserRow(): void {
    this.usersFromExcel = [...this.usersFromExcel, this.createEmptyUser()];
  }

  deleteUserRow(index: number): void {
    if (index < 0 || index >= this.usersFromExcel.length) {
      return;
    }
    const next = [...this.usersFromExcel];
    next.splice(index, 1);
    this.usersFromExcel = next;
  }

  onUserChange(index: number): void {
    if (index < 0 || index >= this.usersFromExcel.length) {
      return;
    }
    const user = this.usersFromExcel[index];
    this.validateUser(user);
  }

  private clearMessages(): void {
    this.errorMessage = '';
    this.warningMessage = '';
    this.successMessage = '';
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFile(files[0]);
    }
  }

  onFileSelected(event: Event): void {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files.length > 0) {
      this.handleFile(target.files[0]);
    }
  }

  private handleFile(file: File): void {
    // Kiểm tra loại file
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel'                                           // .xls
    ];

    if (!allowedTypes.includes(file.type)) {
      this.errorMessage = 'Vui lòng chọn file Excel (.xlsx hoặc .xls)';
      return;
    }

    this.selectedFile = file;
    this.clearMessages();
    this.tempSelectedOrganization = null;
    this.selectedOrganization = null;
    this.usersFromExcel = [];
  }

  removeFile(): void {
    this.selectedFile = null;
    this.usersFromExcel = [];
    this.clearMessages();
    if (this.fileInput?.nativeElement) {
      this.fileInput.nativeElement.value = '';
    }
  }

  async processExcelFile(): Promise<void> {
    if (!this.selectedFile) {
      this.errorMessage = 'Vui lòng chọn file Excel trước khi xử lý';
      return;
    }

    this.isProcessing = true;
    this.clearMessages();

    try {
      const data = await this.readExcelFile(this.selectedFile);
      this.usersFromExcel = this.parseExcelData(data);
      this.successMessage = `Đã đọc thành công ${this.usersFromExcel.length} người dùng từ file Excel ${ this.selectedFile.name }`;
    } catch (error) {
      this.errorMessage = 'Có lỗi xảy ra khi đọc file Excel: ' + (error as Error).message;
      this.usersFromExcel = [];
    } finally {
      this.isProcessing = false;
    }
  }

  private readExcelFile(file: File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = e.target?.result;
          const workbook = XLSX.read(data, { type: 'binary' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          resolve(jsonData);
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => {
        reject(new Error('Không thể đọc file'));
      };

      reader.readAsBinaryString(file);
    });
  }

  private parseExcelData(data: any[]): UserFromExcel[] {
    if (data.length < 2) {
      throw new Error('File Excel không có dữ liệu hoặc thiếu header');
    }

    // Lấy header (dòng đầu tiên)
    const headers = data[0] as string[];

    // Tìm vị trí các cột cần thiết theo yêu cầu mới
    const emailIndex = this.findColumnIndex(headers, ['Email', 'E-mail', 'Địa chỉ email']);
    const passwordIndex = this.findColumnIndex(headers, ['Mật khẩu', 'Password']);
    const fullnameIndex = this.findColumnIndex(headers, ['Tên', 'Họ tên', 'Họ và tên', 'Full Name', 'Name']);
    const phoneIndex = this.findColumnIndex(headers, ['Số điện thoại', 'Phone', 'Điện thoại', 'Phone Number']);
    const genderIndex = this.findColumnIndex(headers, ['Giới tính', 'Gender', 'Nam/Nữ']);
    const dobIndex = this.findColumnIndex(headers, ['Ngày sinh', 'DOB', 'Date of Birth', 'Birthdate']);

    const users: UserFromExcel[] = [];

    // Xử lý từng dòng dữ liệu (bỏ qua header)
    for (let i = 1; i < data.length; i++) {
      const row = data[i] as any[];

      if (
        (!row || row.length === 0) || 
        row.every(cell => {
          return this.cleanString(cell) === '';
        })
      ) {
        continue;
      }

      const genderRaw = genderIndex !== -1 ? row[genderIndex] : '';
      const dobRaw = dobIndex !== -1 ? row[dobIndex] : '';
      const user: UserFromExcel = {
        email: emailIndex !== -1 ? this.cleanString(row[emailIndex]) : '',
        password: passwordIndex !== -1 ? this.cleanString(row[passwordIndex]) : '',
        fullname: fullnameIndex !== -1 ? this.cleanString(row[fullnameIndex]) : '',
        phone: phoneIndex !== -1 ? this.cleanString(row[phoneIndex]) : '',
        gender: this.normalizeGender(genderRaw),
        dob: this.normalizeDate(dobRaw),
        isValid: true,
        errors: []
      };

      // Validate dữ liệu
      this.validateUser(user);
      users.push(user);
    }

    return users;
  }

  private findColumnIndex(headers: string[], possibleNames: string[]): number {
    for (const name of possibleNames) {
      const index = headers.findIndex(header =>
        header &&
        this.cleanString(header).toLowerCase().includes(name.toLowerCase())
      );
      if (index !== -1) {
        return index;
      }
    }
    return -1;
  }  

  private cleanString(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    return String(value).trim();
  }

  private normalizeGender(value: any): 'Nam' | 'Nữ' | '' {
    const v = this.cleanString(value).toLowerCase();
    if (!v) return '';
    if (['nam', 'male', 'm'].includes(v)) return 'Nam';
    if (['nữ', 'nu', 'female', 'f'].includes(v)) return 'Nữ';
    return '';
  }

  private normalizeDate(value: any): string {
    if (value === null || value === undefined || value === '') return '';
    if (typeof value === 'number') {
      // Excel serial date number
      const d = XLSX.SSF.parse_date_code(value as number);
      if (!d) return '';
      const yyyy = d.y.toString().padStart(4, '0');
      const mm = (d.m as number).toString().padStart(2, '0');
      const dd = (d.d as number).toString().padStart(2, '0');
      return `${yyyy}-${mm}-${dd}`;
    }
    const s = this.cleanString(value);
    // Try to parse common formats into YYYY-MM-DD
    const date = new Date(s);
    if (isNaN(date.getTime())) return '';
    const yyyy = date.getFullYear().toString().padStart(4, '0');
    const mm = (date.getMonth() + 1).toString().padStart(2, '0');
    const dd = date.getDate().toString().padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  private validateUser(user: UserFromExcel): void {
    const errors: string[] = [];

    // Validate email
    if (user.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(user.email)) {
        errors.push('Email không hợp lệ');
      }
    } else {
      errors.push('Email là bắt buộc');
    }

    // Validate password
    if (!user.password) {
      errors.push('Mật khẩu là bắt buộc');
    }

    // Validate fullname
    if (!user.fullname || user.fullname.trim() === '') {
      errors.push('Họ và tên là bắt buộc');
    }

    // Validate phone
    if (user.phone) {
      // Only accept 10 digits, must start with 0
      function isValidVietnamesePhoneNumber(number: string): boolean {
        return /^0\d{9}$/.test(number);
      }
      if (!isValidVietnamesePhoneNumber(user.phone)) {
        errors.push('Số điện thoại không hợp lệ (phải là 10 số và bắt đầu bằng 0)');
      }
    }

    // Validate gender
    if (user.gender && user.gender !== 'Nam' && user.gender !== 'Nữ') {
      errors.push('Giới tính không hợp lệ');
    }

    // Validate dob
    if (user.dob) {
      let dateStr = user.dob;
      let date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        const yyyy = date.getFullYear();
        const mm = (date.getMonth() + 1).toString().padStart(2, '0');
        const dd = date.getDate().toString().padStart(2, '0');
        user.dob = `${yyyy}-${mm}-${dd}`;
      } else {
        errors.push('Ngày sinh không hợp lệ');
      }
    }

    user.errors = errors;
    user.isValid = errors.length === 0;
  }

  canSaveUsers(): boolean {
    return this.usersFromExcel.length > 0 && this.usersFromExcel.every(user => user.isValid);
  }

  saveUsers(): void {
    if (!this.selectedOrganization || !this.selectedOrganization.id) {
      this.errorMessage = 'Vui lòng chọn tổ chức trước khi lưu danh sách người dùng';
      this.successMessage = '';
      return;
    }

    if (!this.canSaveUsers()) {
      this.errorMessage = 'Vui lòng kiểm tra lại dữ liệu trước khi lưu';
      this.successMessage = '';
      return;
    }

    const usersToSubmit = this.usersFromExcel.map(({isValid, errors, ...rest}) => rest);

    this._userService.createMultipleUsers(this.selectedOrganization.id, usersToSubmit)
      .subscribe(
        (res: any) => {
          this.successMessage = `Đã lưu thành công ${res.created.length} người dùng`;
          this.errorMessage = '';
          if (res.skipped.count > 0) {
            this.warningMessage = `Đã bỏ qua ${res.skipped.count} người dùng`;
            this.skippedEmails = res.skipped.email;
          } else {
            this.warningMessage = '';
            this.skippedEmails = [];
          }
        },
        (err) => {
          this.errorMessage = err?.error?.message || 'Đã xảy ra lỗi khi lưu người dùng';
          this.successMessage = '';
        }
      );
  }

  clearData(): void {
    this.selectedFile = null;
    this.usersFromExcel = [];
    this.clearMessages();
  }
}
