import { Component, Input, Output, EventEmitter, ViewChild, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { GraphFormState } from '../../types/graph.types';
import { FlatpickrOptions, Ng2FlatpickrComponent } from 'ng2-flatpickr';

@Component({
  selector: 'app-expansion-modal',
  templateUrl: './expansion-modal.component.html',
  styleUrls: ['./expansion-modal.component.scss'],
})
export class ExpansionModalComponent implements AfterViewInit {
  @Input() visible: boolean = false;
  @Input() formState: GraphFormState | null = null;
  @Input() boLocMoiQuanHeOptions: any[] = [];
  @Input() coQuanBanHanhOptions: any[] = [];
  @Input() boLocLoaiVanBanOptions: any[] = [];
  @Input() tinhTrangHieuLucOptions: any[] = [];
  @Input() customYearOptions: FlatpickrOptions = {};

  @ViewChild('modalYearPicker', { static: false }) modalYearPicker?: Ng2FlatpickrComponent;

  @Output() close = new EventEmitter<void>();
  @Output() submit = new EventEmitter<void>();
  @Output() yearChange = new EventEmitter<any>();

  constructor(private cdr: ChangeDetectorRef) {}

  onClose(): void {
    this.close.emit();
  }

  onSubmit(): void {
    // Validate date filter: if a radio is checked, both dateFilterFrom and dateFilterTo must be set
    // Otherwise, set them to null
    if (this.formState) {
      const hasDateFilterMode = this.formState.dateFilterMode === 'ban_hanh' || this.formState.dateFilterMode === 'hieu_luc';
      const hasFullDateRange = 
        this.formState.dateFilterFrom !== null && 
        this.formState.dateFilterFrom !== undefined &&
        this.formState.dateFilterTo !== null && 
        this.formState.dateFilterTo !== undefined;

      // If a radio is checked but date range is incomplete, set dates to null
      if (hasDateFilterMode && !hasFullDateRange) {
        this.formState.dateFilterFrom = null;
        this.formState.dateFilterTo = null;
      }
    }
    
    this.submit.emit();
  }

  ngAfterViewInit(): void {
    // Component initialization
  }

  /**
   * Check if date filter mode is selected (either radio button is checked)
   */
  get isDateFilterModeSelected(): boolean {
    return this.formState?.dateFilterMode === 'ban_hanh' || this.formState?.dateFilterMode === 'hieu_luc';
  }

  /**
   * Watch for date filter mode changes with toggle functionality
   */
  onDateFilterModeChange(mode: 'ban_hanh' | 'hieu_luc', event: Event): void {
    if (!this.formState) return;

    // Toggle: if clicking the same mode, deactivate it
    if (this.formState.dateFilterMode === mode) {
      // Prevent default radio behavior to allow deselection
      event.preventDefault();
      event.stopPropagation();
      this.formState.dateFilterMode = null;
      
      // Clear date values when unchecking
      this.formState.dateFilterFrom = null;
      this.formState.dateFilterTo = null;

      // Manually uncheck all radio buttons in the group
      setTimeout(() => {
        const radioButtons = document.querySelectorAll(
          'input[name="modal-date-filter-mode"]'
        ) as NodeListOf<HTMLInputElement>;
        radioButtons.forEach((radio) => {
          radio.checked = false;
        });
        this.cdr.detectChanges();
      }, 0);
    } else {
      // Set the new mode
      this.formState.dateFilterMode = mode;
      this.cdr.detectChanges();

      // Open datepicker immediately after radio is selected
      setTimeout(() => {
        if (this.modalYearPicker) {
          const flatpickrInstance = (this.modalYearPicker as any).flatpickr;
          if (flatpickrInstance) {
            try {
              flatpickrInstance.open();
            } catch (error) {
              // Ignore errors if flatpickr is not ready
            }
          }
        }
      }, 100); // Small delay to ensure datepicker is rendered
    }
  }

  onYearChange(event: any): void {
    this.yearChange.emit(event);
  }

  onModalClick(event: Event): void {
    event.stopPropagation();
  }
}

