/* ========== Chatbot base ========== */
.chatbot {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 1000px;
  margin: 0 auto;
  padding: 96px 16px 96px;

  &__hero { text-align: center; }

  &__title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: .5rem;
  }

  /* <PERSON><PERSON>ng tìm kiếm (container) */
  &__search {
    position: relative; 
    width: min(820px, 94%);
    margin: 0 auto 18px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__input {
    width: min(720px, 90%);
    height: 44px;
    padding: 0 .75rem;
    border: 1px solid #e5e7eb;
    border-radius: 999px;
    outline: none;
  }

  &__send {
    height: 44px;
    width: 44px;
    border-radius: 999px;
    border: 1px solid #e5e7eb;
    background: #fff;
    cursor: pointer;
  }

  &__chips {
    margin-top: .5rem;
    display: flex;
    flex-wrap: wrap;
    gap: .5rem;
    justify-content: center;
  }
}

/* ========== Chips & Messages ========== */
.chip {
  padding: .35rem .6rem;
  border-radius: 999px;
  border: 1px solid #e5e7eb;
  background: #fff;
  font-size: .85rem;
  cursor: pointer;
}
.chatbot__messages {
  width: min(900px, 95%);
  margin: 0 auto;
  height: 420px;
  overflow: auto;
  border: 1px solid #eef2f7;
  border-radius: 16px;
  padding: 1rem;
  background: #fff;
}
.msg {
  display: flex;
  margin-bottom: .75rem;

  &__bubble {
    max-width: 80%;
    padding: .6rem .8rem;
    border-radius: 14px;
    background: #f3f4f6;
    line-height: 1.45;
    white-space: pre-wrap;
  }
  &--user {
    justify-content: flex-end;
    .msg__bubble { background: #1f6feb; color: #fff; }
  }
}

.chatbot__title {
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  margin: 0 0 24px;
}

.chatbot__input {
  width: 100%;
  height: 108px;
  padding: 12px 72px 26px 26px;
  border: 1px solid rgba(2,18,43,.12);
  border-radius: 14px;
  background: #fff;
  font-size: 16px;
  line-height: 22px;
  box-shadow: 0 8px 18px rgba(2,18,43,.06);
  outline: none;

  &:focus{
    border-color: rgba(2,18,43,.22);
    box-shadow: 0 10px 22px rgba(2,18,43,.10);
  }
}

.chatbot__input::placeholder,
.chatbot__input::-webkit-input-placeholder,
.chatbot__input::-moz-placeholder,
.chatbot__input:-ms-input-placeholder,
.chatbot__input:-moz-placeholder{
  color:#A3A9B3; font-size:16px; line-height:20px;
}

.chatbot__send{
  position: absolute;
  right: 16px;
  bottom: 12px;
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
  border: 1px solid #e5e7eb;              
  background: #F3F4F6;  
  color: #6B7280;
  display: grid;
  place-items: center;
  cursor: pointer;
  transition:
    background-color .15s ease,
    color .15s ease,
    box-shadow .15s ease,
    transform .1s ease;

  svg{ display: block; }

  &:hover{
    background: #E5E7EB;
    color: #111827;
  }
  &:focus-visible{
    outline: none;
    box-shadow: 0 0 0 3px rgba(14,165,233,.25); 
    background: #ffffff;
  }
  &:active{ transform: translateY(1px); }
}

.chip {
  padding: 8px 14px;
  border-radius: 999px;
  border: 1px solid rgba(2,18,43,.12);
  background: #fff;
  cursor: pointer;
  font-size: 14px;
}

@media (max-width: 480px){
  .chatbot__input{
    height: 76px;
    padding: 10px 64px 22px 16px;
    font-size: 16px;
    line-height: 20px;
  }
  .chatbot__send{
    right: 12px;
    bottom: 10px;
    width: 34px;
    height: 34px;
  }
}
/* ===== Chatbot – base ===== */
:host{ display:block; width:100%; }

:root{
  --cb-min: 520px;
  --cb-fluid: 68vw;
  --cb-max: 920px;
}

// .chatbot{
//   width: clamp(320px, 92vw, 760px);
//   width: clamp(var(--cb-min), var(--cb-fluid), var(--cb-max));
//   margin: 0 auto;
//   padding: clamp(32px, 6vh, 56px) 0 0;
//   max-width: none;
// }
.chatbot{
  width: clamp(var(--cb-min), var(--cb-fluid), var(--cb-max));
  margin: 0 auto;
  padding: clamp(80px, 12vh, 120px) 0 clamp(72px, 11vh, 120px);
  max-width: none;
}
/* ===== Search row ===== */
.chatbot__hero{ text-align:center; }
.chatbot__title{
  font-size: clamp(22px, 2.4vw, 32px);
  font-weight:700;
  margin: 0 0 18px;
}

.chatbot__input{
  width: 100%;
  height: clamp(52px, 6.5vh, 56px);
  padding: 12px 56px 12px 26px;
  border: 1px solid rgba(2,18,43,.12);
  border-radius: 14px;
  background: #fff;
  font-size: 16px; line-height: 22px;
  box-shadow: 0 8px 18px rgba(2,18,43,.06);
  outline: none;
  box-sizing: border-box;
  resize: none;
}
.chatbot__input:focus{
  border-color: rgba(2,18,43,.22);
  box-shadow: 0 10px 22px rgba(2,18,43,.10);
}

.chatbot__send{
  position: absolute; right: 12px; top: 50%;
  transform: translateY(-50%);
  width: 36px; height: 36px;
  padding: 0; border-radius: 50%;
  border: 1px solid #e5e7eb;
  background: #F3F4F6; color: #6B7280;
  display: grid; place-items: center;
}

/* chips */
.chatbot__chips{
  display:flex;
  flex-wrap:wrap;
  gap:10px;
  justify-content:center;
  margin-top:10px;
}
.chip{ padding:8px 14px; border-radius:999px; border:1px solid rgba(2,18,43,.12); background:#fff; font-size:14px; }

/* messages */
.chatbot__messages{
  width: 100%;
  margin: 14px auto 0;
  height: 420px; overflow:auto;
  border:1px solid #eef2f7; border-radius:16px; padding:1rem; background:#fff;
  box-sizing: border-box;
}

.chatbot__search:has(.chatbot__input:not(:placeholder-shown)) .chatbot__send:not(:disabled) {
  background: #E5E7EB;
  color: #111827;
  box-shadow: 0 10px 22px rgba(2,18,43,.10);
  transform: none;
}

.chatbot__search:has(.chatbot__input:not(:placeholder-shown)) .chatbot__send:not(:disabled) svg {
  transform: translateX(1px);
  transition: transform .15s ease;
}

.chatbot__search .chatbot__send{
  right: 16px !important;
  bottom: 12px !important;
  top: auto !important;
  transform: none !important;
}

/* ======= DEFAULT======= */
.chatbot__search{
  position: relative;
  border-radius: 16px;
  padding: 12px;
  background: #fff;
  box-shadow: 0 6px 14px rgba(2,18,43,.06);
  transition: box-shadow .2s ease, padding .2s ease;
}

.chatbot__search::before{
  content:"";
  position:absolute; inset:0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(
    90deg,
    #2384C8 0%,
    #D1D0D5 50%,
    #F8D018 100%
  );
  background-size: 300% 100%;
  opacity: .8;
  -webkit-mask:
    linear-gradient(#000 0 0) content-box,
    linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
          mask-composite: exclude;
  pointer-events:none;
  animation: cb-border-soft 15s ease-in-out infinite;
}

@keyframes cb-border-soft{
  0%   { background-position:   0% 0; }
  50%  { background-position: 100% 0; }
  100% { background-position:   0% 0; }
}


.chatbot__search .chatbot__input{
  height: 108px !important;
  padding: 16px 72px 20px 26px !important;
  border-radius: 12px;
  border: 1px solid transparent;
  box-shadow: 0 8px 18px rgba(2,18,43,.06);
}
.chatbot__search .chatbot__send{
  right: 16px;
  bottom: 18px;
  top: auto;
  transform: none;
}

.chatbot__chips{ 
  display: none;
  opacity: 0; transform: translateY(-6px);
  transition: opacity .2s ease, transform .2s ease;
}

/* ======= FOCUS STATE ======= */
.chatbot__search:focus-within{
  padding: 12px;                   
  box-shadow: 0 12px 24px rgba(2,18,43,.08);
}

/* viền gradient chạy khi focus */
// .chatbot__search:focus-within::before{
//   padding: 2px;
//   opacity: 1;
//   animation: cb-border-move 3s linear infinite;
// }

@keyframes cb-border-move{
  0%{ background-position: 200% 0; }
  100%{ background-position: 0% 0; }
}

.chatbot__search:focus-within .chatbot__input{
  height: 108px !important;
  padding: 16px 72px 20px 26px !important;
}
.chatbot__search:focus-within .chatbot__send{
  bottom: 18px !important; top: auto !important; transform: none !important;
}

.chatbot__hero:has(.chatbot__search:focus-within) .chatbot__chips{
  display: flex;
  opacity: 1; transform: translateY(0);
}

@media (max-width: 480px){
  .chatbot__search .chatbot__input{ height: 52px !important; padding: 10px 52px 12px 14px !important; }
  .chatbot__search:focus-within .chatbot__input{ height: 76px !important; padding: 12px 52px 16px 14px !important; }
  .chatbot__search .chatbot__send{ right:10px; bottom:10px; }
}

@media (max-width: 576px){

  .chatbot{ padding-top: 28px; }

  .chatbot__title{
    font-size: 18px;            /* gọn hơn */
    margin-bottom: 12px;
  }

  .chatbot__search{
    padding: 6px;           
    border-radius: 12px;
    box-shadow: 0 6px 12px rgba(2,18,43,.06);
  }
  .chatbot__search::before{
    padding: 1px; 
    opacity: .7;
    border-radius: inherit;
  }

  /* Textarea: thấp, chữ không bị dính mép */
  .chatbot__search .chatbot__input{
    height: 52px !important;
    padding: 10px 44px 12px 14px !important;  /* ↑top ↓bottom vừa mắt */
    font-size: 15px;
    line-height: 20px;
    border-radius: 10px;
  }

  /* Nút gửi: nhỏ & sát góc hơn */
  .chatbot__search .chatbot__send{
    width: 32px; height: 32px;
    right: 8px; bottom: 8px;
  }

  /* Focus: nở ra một nhịp, viền chạy */
  .chatbot__search:focus-within{
    padding: 8px;
    box-shadow: 0 10px 18px rgba(2,18,43,.08);
  }
  .chatbot__search:focus-within::before{
    padding: 1.5px;
    animation: cb-border-move 3s linear infinite;
  }
  .chatbot__search:focus-within .chatbot__input{
    height: 68px !important;
    padding: 12px 48px 14px 14px !important;
  }

  /* Chips: ẩn mặc định, chỉ hiện khi focus */
  .chatbot__hero:has(.chatbot__search:focus-within) .chatbot__chips{
    display:flex; opacity:1; transform:translateY(0);
  }
}


/* mobile */
@media (max-width: 480px){
  .chatbot__search .chatbot__input{
    height: 76px !important;
    padding: 4px 52px 24px 14px !important;
  }
  .chatbot__search .chatbot__send{ right: 10px !important; bottom: 10px !important; }
}

/* ===== Responsive – lớn → nhỏ ===== */
@media (min-width: 1200px){
  .chatbot{ width: clamp(560px, 60vw, 980px); }
}

@media (max-width: 992px){
  :root{
    --cb-min: 460px;
    --cb-fluid: 84vw;
    --cb-max: 760px;
  }
}

@media (max-width: 768px){
  :root{
    --cb-min: 320px;
    --cb-fluid: 94vw;
    --cb-max: 680px;
  }
}

@media (max-width: 480px){
  .chatbot__input{ height: 52px; padding: 10px 52px 10px 14px; }
  .chatbot__send{ width: 34px; height: 34px; right: 10px; }
}

/* Ở đây CHỈNH để chips luôn hiển thị */
.chatbot__chips{
  display: flex !important;
  gap: 10px;
  justify-content: center;
  margin-top: 12px;
  position: relative;
  z-index: 3;
  opacity: 1;
  max-height: none;
  overflow: visible;
  transform: none;
  visibility: visible;
  transition: none;
}
// .chatbot__hero:has(.chatbot__search:focus-within) .chatbot__chips,
// .chatbot__chips:hover,
// .chatbot__chips:focus-within{
//   opacity: 1; max-height: 160px; transform: translateY(0);
//   visibility: visible; transition: opacity .2s, transform .2s, max-height .2s, visibility 0s 0s;
// }
.chip{ user-select: none; }
.chatbot__hero:has(.chatbot__chips:hover),
.chatbot__hero:has(.chatbot__chips:focus-within),
.chatbot__hero:has(.chatbot__chips .chip:active),
.chatbot__hero:has(.chatbot__chips .chip:focus) {
}

/* mở rộng khung search khi đang tương tác chips */
.chatbot__hero:has(.chatbot__chips:hover) .chatbot__search,
.chatbot__hero:has(.chatbot__chips:focus-within) .chatbot__search,
.chatbot__hero:has(.chatbot__chips .chip:active) .chatbot__search,
.chatbot__hero:has(.chatbot__chips .chip:focus) .chatbot__search{
  padding: 12px;
  box-shadow: 0 12px 24px rgba(2,18,43,.08);
}
.chatbot__hero:has(.chatbot__chips:hover) .chatbot__search::before,
.chatbot__hero:has(.chatbot__chips:focus-within) .chatbot__search::before,
.chatbot__hero:has(.chatbot__chips .chip:active) .chatbot__search::before,
.chatbot__hero:has(.chatbot__chips .chip:focus) .chatbot__search::before{
  padding: 2px; opacity: 1;
  animation: cb-border-move 3s linear infinite;
}
.chatbot__hero:has(.chatbot__chips:hover) .chatbot__search .chatbot__input,
.chatbot__hero:has(.chatbot__chips:focus-within) .chatbot__search .chatbot__input,
.chatbot__hero:has(.chatbot__chips .chip:active) .chatbot__search .chatbot__input,
.chatbot__hero:has(.chatbot__chips .chip:focus) .chatbot__search .chatbot__input{
  height: 108px !important;
  padding: 16px 72px 28px 26px !important;
}

.chatbot__search:not(:focus-within) .chatbot__input{
  height: 108px !important;
  padding: 16px 72px 20px 26px !important; 
  line-height: 20px;
}

.chatbot__search:not(:focus-within) .chatbot__send{
  top: auto;
  bottom: 18px;
  transform: none;
  right: 16px !important;
}
.chatbot__search .chatbot__send,
.chatbot__search:not(:focus-within) .chatbot__send,
.chatbot__hero:has(.chatbot__chips:hover) .chatbot__search .chatbot__send,
.chatbot__hero:has(.chatbot__chips:focus-within) .chatbot__search .chatbot__send,
.chatbot__hero:has(.chatbot__chips .chip:active) .chatbot__search .chatbot__send,
.chatbot__hero:has(.chatbot__chips .chip:focus) .chatbot__search .chatbot__send {
  top: auto !important;
  bottom: 18px !important;
  right: 16px !important;
  transform: none !important;
}

.chatbot__placeholder{
  position: absolute;
  left: 40px; 
  right: 64px;
  top: 30px; 
  transform: none;
  font-size: 16px;
  line-height: 20px;
  color: #A3A9B3;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  pointer-events: none;
  opacity: .9;
  transition: opacity .35s ease, transform .35s ease;
}

.chatbot__placeholder.is-fading-out{
  opacity: 0;
  transform: translateY(-4px);
}

@media (max-width: 480px){
  .chatbot__placeholder{
    left: 24px;
    right: 54px;
    font-size: 14px;
    top: 20px;
  }
}
// .chatbot__title {
//   /* xoá mọi nền / shadow / strip phía sau text */
//   background: transparent !important;
//   box-shadow: none !important;
//   -webkit-background-clip: initial !important;
//   background-clip: initial !important;
//   padding: 0 !important;
// }

.chatbot__title::before,
.chatbot__title::after {
  content: none !important;
  background: transparent !important;
  box-shadow: none !important;
}
