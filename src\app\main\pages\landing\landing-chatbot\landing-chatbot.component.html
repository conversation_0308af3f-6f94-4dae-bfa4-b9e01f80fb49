<div class="chatbot">
  <div class="chatbot__hero">
    <h2 class="chatbot__title">T<PERSON><PERSON> có thể giúp gì cho bạn ?</h2>

    <div class="chatbot__search">
      <textarea
        #inputEl
        class="chatbot__input"
        placeholder=" "
        [formControl]="form.controls['prompt']"
        (keydown)="onEnter($event)"
        (input)="onInputChange()"
      ></textarea>

      <div
        class="chatbot__placeholder"
        *ngIf="!form.value?.prompt"
        [class.is-fading-out]="isFadingOut"
        (transitionend)="onPlaceholderTransitionEnd($event)">
        {{ currentPlaceholder }}
      </div>

      <button
        type="button"
        class="chatbot__send"
        title="Gửi"
        aria-label="Gửi truy vấn"
        (click)="send()"
        [disabled]="loading || !form.controls['prompt']?.value?.trim()">
        <svg viewBox="0 0 24 24" width="16" height="16" aria-hidden="true">
          <path
            d="M3 11.5l17-8a1 1 0 011.3 1.3l-8 17a1 1 0 0 1-1.8-.1l-2.5-6.2-6.2-2.5a1 1 0 0 1 .1-1.5z"
            fill="currentColor"
          />
        </svg>
      </button>
    </div>

    <div class="chatbot__chips">
      <button class="chip" *ngFor="let s of suggestions" (click)="pickSuggestion(s)">
        {{ s }}
      </button>
    </div>
  </div>
</div>
