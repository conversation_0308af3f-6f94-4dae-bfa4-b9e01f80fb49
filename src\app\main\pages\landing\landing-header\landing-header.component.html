<!-- Header Section (giữ nguyên class & cấu trúc) -->
<header class="landing__header" [class.is-scrolled]="isScrolled">
  <button
    class="landing__burger"
    type="button"
    aria-label="Mở menu"
    [attr.aria-expanded]="isMenuOpen"
    aria-controls="landing-drawer"
    (click)="toggleMenu()">
    <span></span><span></span><span></span>
  </button>

  <div
    class="landing__header-logo"
    role="button"
    tabindex="0"
    (click)="gotoLanding()"
    (keydown.enter)="gotoLanding()"
    (keydown.space)="gotoLanding(); $event.preventDefault()"
  >
    <figure class="landing__header-logo-image">
      <img [src]="logoSrc" alt="logo" />
    </figure>
    <span class="landing__header-logo-separator"></span>
    <span class="landing__header-logo-text">{{ brandText }}</span>
  </div>

  <nav class="landing__header-nav" role="navigation" aria-label="Điều hướng đầu trang" *ngIf="showNav">
    <ul class="landing__header-nav-list">
      <li class="landing__header-nav-item" [class.active]="activeSection==='chatbot'"     (click)="select('chatbot')">Truy vấn</li>
      <li class="landing__header-nav-item" [class.active]="activeSection==='information'" (click)="select('information')">Thông tin</li>
      <li class="landing__header-nav-item" [class.active]="activeSection==='articles'"    (click)="select('articles')">Bài viết</li>
      <li class="landing__header-nav-item" [class.active]="activeSection==='mission'"     (click)="select('mission')">Sứ mệnh</li>
      <!-- <li class="landing__header-nav-item" [class.active]="activeSection==='contact'"     (click)="select('contact')">Liên hệ</li> -->
    </ul>
  </nav>

  <a
    class="landing__header-action"
    [routerLink]="['']"
    [attr.data-url]="fullLoginUrl"
  >
    <button class="landing__header-login-btn">Trải nghiệm ngay</button>
  </a>
</header>

<!-- Drawer & backdrop (di động) -->
<div class="landing__drawer-backdrop" *ngIf="isMenuOpen" (click)="closeMenu()"></div>
<aside
  id="landing-drawer"
  class="landing__drawer"
  [class.open]="isMenuOpen"
  (keydown.escape)="closeMenu()"
  tabindex="-1">
  <div class="landing__drawer-header">
    <span class="landing__drawer-title">Danh mục</span>
    <button type="button" class="landing__drawer-close" aria-label="Đóng menu" (click)="closeMenu()">✕</button>
  </div>

  <nav class="landing__drawer-nav" role="navigation" aria-label="Điều hướng di động">
    <ul class="landing__drawer-list" *ngIf="showNav; else onlyLogin">
      <li class="landing__drawer-item" (click)="select('chatbot')">Truy vấn</li>
      <li class="landing__drawer-item" (click)="select('information')">Thông tin</li>
      <li class="landing__drawer-item" (click)="select('mission')">Sứ mệnh</li>
      <li class="landing__drawer-item" (click)="select('articles')">Bài viết</li>
      <li class="landing__drawer-item" (click)="select('contact')">Liên hệ</li>
      <li class="landing__drawer-item landing__drawer-login" (click)="gotoLogin()">Đăng nhập</li>
    </ul>

    <ng-template #onlyLogin>
      <li class="landing__drawer-item landing__drawer-login" (click)="gotoLogin()">Đăng nhập</li>
      <li class="landing__drawer-item" (click)="gotoLanding()">Về trang chủ</li>
    </ng-template>
  </nav>
</aside>
